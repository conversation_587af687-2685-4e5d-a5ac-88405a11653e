#!/usr/bin/env python3
"""
测试方法A的效果
"""

import numpy as np
from scipy.spatial.transform import Rotation as R

def test_method_a_effect():
    """测试方法A的预期效果"""
    
    print("=== 测试方法A：直接使用ICP结果 ===")
    
    # 模拟自车左转5度，前进1米的场景
    ego_translation = np.array([1.0, 0.0, 0.0])  # 前进1米
    ego_rotation_angle = np.radians(5)  # 左转5度
    ego_rotation_matrix = R.from_euler('z', ego_rotation_angle).as_matrix()
    ego_transform = np.eye(4)
    ego_transform[:3, :3] = ego_rotation_matrix
    ego_transform[:3, 3] = ego_translation
    
    print(f"自车真实运动（左转5度，前进1米）:")
    for i, row in enumerate(ego_transform):
        print(f"  [{i}] {row}")
    
    # ICP通常给出从PCD1到PCD2的变换
    # 如果自车运动了，那么同一个静止物体在两个点云中的位置会不同
    # ICP会找到这个位置差异的变换
    
    # 静止物体在世界坐标系中的位置
    static_object_world = np.array([14.3054, 2.9938, 3.9605])
    
    # 在第一帧点云中，物体相对自车的位置
    object_in_pcd1 = static_object_world.copy()
    
    # 自车运动后，同一个静止物体在第二帧点云中相对自车的位置
    # 需要应用自车运动的逆变换
    ego_inv = np.linalg.inv(ego_transform)
    object_in_pcd1_homo = np.append(object_in_pcd1, 1.0)
    object_in_pcd2_homo = np.dot(ego_inv, object_in_pcd1_homo)
    object_in_pcd2 = object_in_pcd2_homo[:3]
    
    print(f"\n物体在两个点云中的位置:")
    print(f"PCD1中位置: {object_in_pcd1}")
    print(f"PCD2中位置: {object_in_pcd2}")
    
    # ICP会找到从PCD1到PCD2的变换
    # 即：icp_transform * object_in_pcd1 ≈ object_in_pcd2
    # 所以：icp_transform ≈ ego_inv
    
    icp_transform = ego_inv
    print(f"\nICP找到的变换（≈ ego_inv）:")
    for i, row in enumerate(icp_transform):
        print(f"  [{i}] {row}")
    
    # 方法A：直接使用ICP结果
    # new_position = icp_transform * original_position
    # 这相当于：new_position = ego_inv * original_position
    
    method_a_result_homo = np.dot(icp_transform, object_in_pcd1_homo)
    method_a_result = method_a_result_homo[:3]
    method_a_change = method_a_result - static_object_world
    
    print(f"\n方法A结果:")
    print(f"原始位置: {static_object_world}")
    print(f"新位置: {method_a_result}")
    print(f"位置变化: {method_a_change}")
    print(f"Y变化: {method_a_change[1]:.4f} ({'右移' if method_a_change[1] > 0 else '左移'})")
    print(f"总变化距离: {np.linalg.norm(method_a_change):.4f}m")
    
    # 物理解释
    print(f"\n=== 物理解释 ===")
    print("方法A的含义：")
    print("1. ICP给出从PCD1到PCD2的变换")
    print("2. 这个变换反映了静止物体在两个点云中的位置差异")
    print("3. 直接应用这个变换，得到物体在第二帧中的预期位置")
    print("4. 这相当于补偿了自车运动对物体观测位置的影响")
    
    print(f"\n自车左转时的预期：")
    print("- 静止物体应该相对向右偏移（从自车视角）")
    print("- 在世界坐标系中，可能表现为Y坐标的变化")
    print("- 具体方向取决于坐标系定义")
    
    if method_a_change[1] < 0:
        print(f"✅ 方法A结果：Y坐标减少（可能对应向右偏移）")
    else:
        print(f"❌ 方法A结果：Y坐标增加（可能对应向左偏移）")

def create_api_test():
    """创建API测试"""
    
    print(f"\n=== API测试 ===")
    
    test_data = {
        "pcd1Url": "path/to/your/frame1.pcd",
        "pcd2Url": "path/to/your/frame2.pcd",
        "objects": [
            {
                "objectId": "method_a_test",
                "center3D": {"x": 14.3054, "y": 2.9938, "z": 3.9605},
                "size3D": {"x": 6.7784, "y": 3.0099, "z": 7.9209},
                "rotation3D": {"x": 0.0, "y": 0.0, "z": -0.192}
            }
        ]
    }
    
    import json
    print("测试数据:")
    print(json.dumps(test_data, indent=2))
    
    print(f"\n预期结果（方法A）:")
    print("- 默认使用方法A（直接ICP）")
    print("- center3D应该显示新的位置")
    print("- compensation_test中仍会显示所有三种方法的对比")
    print("- chosen_method应该显示'A (direct ICP)'")

if __name__ == "__main__":
    test_method_a_effect()
    create_api_test()
    
    print(f"\n=== 使用说明 ===")
    print("1. 启动修改后的服务器:")
    print("   python app_final_fix.py  # 端口5004")
    print("2. 发送API请求")
    print("3. 观察结果:")
    print("   - center3D现在使用方法A的结果")
    print("   - 应该看到位置有合理的变化")
    print("   - 不再是原始位置不变")
    print("4. 如果效果好，我们就确定使用方法A")
    print("5. 如果还不对，可以尝试方法B")
    
    print(f"\n=== 预期改善 ===")
    print("✅ 位置会有变化（不再是0.0000m）")
    print("✅ 变化方向应该符合物理直觉")
    print("✅ 不再出现'往右多偏移'的问题")
    print("✅ 静止物体位置跟随自车运动合理调整")
