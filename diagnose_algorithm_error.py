#!/usr/bin/env python3
"""
诊断算法错误 - 分析静止物体补偿逻辑
"""

import numpy as np
from scipy.spatial.transform import Rotation as R

def analyze_static_object_compensation():
    """分析静止物体补偿的数学逻辑"""
    
    print("=== 静止物体补偿逻辑分析 ===")
    
    # 场景：自车左转5度，向前移动1米
    print("场景设定：自车左转5度，向前移动1米")
    
    # 自车真实运动（从自车坐标系看）
    ego_translation = np.array([1.0, 0.0, 0.0])  # 向前1米
    ego_rotation_angle = np.radians(5)  # 左转5度
    
    # 构建自车变换矩阵（自车坐标系变换）
    ego_rotation_matrix = R.from_euler('z', ego_rotation_angle).as_matrix()
    ego_transform = np.eye(4)
    ego_transform[:3, :3] = ego_rotation_matrix
    ego_transform[:3, 3] = ego_translation
    
    print(f"\n自车变换矩阵（自车运动）:")
    for i, row in enumerate(ego_transform):
        print(f"  [{i}] {row}")
    
    # 静止物体在世界坐标系中的位置
    static_object_world = np.array([14.3054, 2.9938, 3.9605])
    print(f"\n静止物体世界坐标: {static_object_world}")
    
    # 问题分析：ICP给出的是什么变换？
    print(f"\n=== ICP变换分析 ===")
    print("ICP配准通常计算：T * source = target")
    print("即：T * PCD1 = PCD2")
    print("这个T表示从PCD1到PCD2的变换")
    
    # 如果自车左转向前，点云的变化应该是：
    # - 远处物体在新点云中会出现在相对左侧（因为自车右转了视角）
    # - 物体在新点云中会出现在相对后方（因为自车前进了）
    
    # 模拟点云变换
    print(f"\n=== 点云变换模拟 ===")
    
    # 假设静止物体在第一帧点云中的位置（相对自车）
    object_in_pcd1 = static_object_world.copy()
    
    # 自车运动后，同一个静止物体在第二帧点云中的相对位置
    # 需要应用自车运动的逆变换
    ego_inv = np.linalg.inv(ego_transform)
    object_in_pcd1_homo = np.append(object_in_pcd1, 1.0)
    object_in_pcd2_homo = np.dot(ego_inv, object_in_pcd1_homo)
    object_in_pcd2 = object_in_pcd2_homo[:3]
    
    print(f"物体在PCD1中位置: {object_in_pcd1}")
    print(f"物体在PCD2中位置: {object_in_pcd2}")
    
    pcd_change = object_in_pcd2 - object_in_pcd1
    print(f"点云中物体位置变化: {pcd_change}")
    print(f"Y方向变化: {pcd_change[1]:.4f} ({'右移' if pcd_change[1] > 0 else '左移'})")
    
    # ICP会找到从PCD1到PCD2的变换
    # 即：icp_transform * object_in_pcd1 = object_in_pcd2
    # 所以：icp_transform = ego_inv
    
    icp_transform = ego_inv
    print(f"\nICP应该找到的变换矩阵:")
    for i, row in enumerate(icp_transform):
        print(f"  [{i}] {row}")
    
    # 验证
    test_result = np.dot(icp_transform, object_in_pcd1_homo)
    print(f"验证：ICP变换 * PCD1位置 = {test_result[:3]}")
    print(f"应该等于PCD2位置: {object_in_pcd2}")
    print(f"误差: {np.linalg.norm(test_result[:3] - object_in_pcd2):.6f}")

def analyze_current_algorithm_error():
    """分析当前算法的错误"""
    
    print(f"\n=== 当前算法错误分析 ===")
    
    # 模拟当前算法的逻辑
    print("当前算法逻辑：")
    print("1. ICP得到变换：T_icp")
    print("2. 直接使用T_icp作为自车变换")
    print("3. 计算T_icp的逆：T_icp_inv")
    print("4. 应用T_icp_inv到静止物体：new_pos = T_icp_inv * old_pos")
    
    # 但是根据上面的分析，T_icp实际上已经是ego_inv了
    # 所以当前算法实际上是：
    # new_pos = inv(ego_inv) * old_pos = ego * old_pos
    
    print(f"\n问题：")
    print("如果ICP给出的T_icp = ego_inv（自车运动的逆）")
    print("那么算法计算：new_pos = inv(T_icp) * old_pos = inv(ego_inv) * old_pos = ego * old_pos")
    print("这相当于把自车运动又应用了一次，而不是补偿！")
    
    # 正确的逻辑应该是：
    print(f"\n正确逻辑：")
    print("如果ICP给出T_icp = ego_inv")
    print("那么静止物体补偿应该：new_pos = T_icp * old_pos = ego_inv * old_pos")
    print("这样才能正确补偿自车运动")

def test_correct_algorithm():
    """测试正确的算法"""
    
    print(f"\n=== 测试正确算法 ===")
    
    # 自车运动
    ego_translation = np.array([1.0, 0.0, 0.0])
    ego_rotation_angle = np.radians(5)
    ego_rotation_matrix = R.from_euler('z', ego_rotation_angle).as_matrix()
    ego_transform = np.eye(4)
    ego_transform[:3, :3] = ego_rotation_matrix
    ego_transform[:3, 3] = ego_translation
    
    # ICP结果（应该是ego的逆）
    icp_transform = np.linalg.inv(ego_transform)
    
    # 静止物体
    static_object = np.array([14.3054, 2.9938, 3.9605])
    static_object_homo = np.append(static_object, 1.0)
    
    print(f"静止物体原始位置: {static_object}")
    
    # 错误方法（当前算法）
    wrong_transform = np.linalg.inv(icp_transform)  # 这等于ego_transform
    wrong_result = np.dot(wrong_transform, static_object_homo)
    wrong_change = wrong_result[:3] - static_object
    
    print(f"\n错误方法（当前算法）:")
    print(f"  新位置: {wrong_result[:3]}")
    print(f"  位置变化: {wrong_change}")
    print(f"  Y变化: {wrong_change[1]:.4f} ({'右移' if wrong_change[1] > 0 else '左移'})")
    
    # 正确方法
    correct_result = np.dot(icp_transform, static_object_homo)
    correct_change = correct_result[:3] - static_object
    
    print(f"\n正确方法:")
    print(f"  新位置: {correct_result[:3]}")
    print(f"  位置变化: {correct_change}")
    print(f"  Y变化: {correct_change[1]:.4f} ({'右移' if correct_change[1] > 0 else '左移'})")
    
    print(f"\n预期：自车左转时，静止物体应该相对向右移动")
    if correct_change[1] > 0:
        print("✅ 正确方法给出了向右移动")
    else:
        print("❌ 正确方法给出了向左移动")
    
    if wrong_change[1] > 0:
        print("❌ 错误方法给出了向右移动（过度补偿）")
    else:
        print("❌ 错误方法给出了向左移动")

def suggest_fix():
    """建议修复方案"""
    
    print(f"\n=== 修复方案 ===")
    print("问题：当前算法对ICP结果进行了双重逆变换")
    print("修复：直接使用ICP结果作为静止物体的变换")
    
    print(f"\n代码修改：")
    print("当前代码：")
    print("  ego_transform_inv = np.linalg.inv(ego_transform)")
    print("  new_center = ego_transform_inv @ bbox_center_homo")
    
    print("修改为：")
    print("  # 直接使用ICP结果，不再求逆")
    print("  new_center = ego_transform @ bbox_center_homo")
    
    print(f"\n或者：")
    print("如果ICP给出的是正向变换，则：")
    print("  ego_transform_for_compensation = np.linalg.inv(ego_transform)")
    print("  new_center = ego_transform_for_compensation @ bbox_center_homo")

if __name__ == "__main__":
    analyze_static_object_compensation()
    analyze_current_algorithm_error()
    test_correct_algorithm()
    suggest_fix()
    
    print(f"\n=== 总结 ===")
    print("问题：算法对自车运动进行了双重补偿")
    print("原因：ICP结果已经是逆变换，但算法又求了一次逆")
    print("修复：直接使用ICP结果，或者正确理解ICP变换的含义")
    print("验证：修复后，自车左转时静止物体应该适度向右偏移")
