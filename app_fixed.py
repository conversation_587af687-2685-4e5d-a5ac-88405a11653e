#!/usr/bin/env python3
"""
修复后的算法 - 专注于静止物体处理
"""

from flask import Flask, request, jsonify
import numpy as np
from scipy.spatial.transform import Rotation as R
import open3d as o3d
import tempfile
import requests
import os
import time
import shutil
import traceback
import logging
from datetime import datetime
import uuid
import json

app = Flask(__name__)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('fixed_object_tracking.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# 全局变量
current_request_id = None

def log_with_timestamp(message, level="INFO", obj_id=None):
    """带时间戳的日志输出"""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]
    prefix = f"[{timestamp}]"
    if current_request_id:
        prefix += f"[REQ:{current_request_id[:8]}]"
    if obj_id:
        prefix += f"[OBJ:{obj_id}]"
    
    full_message = f"{prefix} {message}"
    
    if level == "ERROR":
        logger.error(full_message)
    elif level == "WARNING":
        logger.warning(full_message)
    else:
        logger.info(full_message)
    
    print(full_message)

def log_matrix(matrix, name, obj_id=None):
    """记录矩阵数据"""
    log_with_timestamp(f"{name}:", obj_id=obj_id)
    for i, row in enumerate(matrix):
        row_str = " ".join([f"{val:8.4f}" for val in row])
        log_with_timestamp(f"  [{i}] [{row_str}]", obj_id=obj_id)

def log_vector(vector, name, obj_id=None):
    """记录向量数据"""
    vector_str = " ".join([f"{val:8.4f}" for val in vector])
    log_with_timestamp(f"{name}: [{vector_str}]", obj_id=obj_id)

def parse_ego_pose(ego_pose_data):
    """解析自车位姿数据"""
    try:
        if "transform_matrix" in ego_pose_data:
            return np.array(ego_pose_data["transform_matrix"])
        
        elif "translation" in ego_pose_data and "rotation" in ego_pose_data:
            translation = np.array(ego_pose_data["translation"])
            rotation = np.array(ego_pose_data["rotation"])
            
            rotation_matrix = R.from_euler('xyz', rotation).as_matrix()
            transform = np.eye(4)
            transform[:3, :3] = rotation_matrix
            transform[:3, 3] = translation
            return transform
        
        else:
            log_with_timestamp("Unknown ego pose data format", "ERROR")
            return None
            
    except Exception as e:
        log_with_timestamp(f"Failed to parse ego pose: {e}", "ERROR")
        return None

def download_file_from_url(url, temp_dir):
    """下载文件"""
    log_with_timestamp(f"Starting download: {url}")
    
    headers = {'User-Agent': 'Mozilla/5.0 (compatible; FileDownloader/1.0)'}

    try:
        download_start = time.time()
        with requests.get(url, stream=True, timeout=30, headers=headers) as response:
            response.raise_for_status()
            
            suffix = ".pcd"
            temp_file = tempfile.NamedTemporaryFile(dir=temp_dir, suffix=suffix, delete=False)

            with open(temp_file.name, 'wb') as f:
                shutil.copyfileobj(response.raw, f)
            
            download_end = time.time()
            download_time = download_end - download_start
            file_size = os.path.getsize(temp_file.name) / 1024 / 1024  # MB
            
            log_with_timestamp(f"Download completed: {temp_file.name}")
            log_with_timestamp(f"Download time: {download_time:.3f}s, size: {file_size:.2f}MB")
            
            return temp_file.name

    except Exception as e:
        error_msg = f"Download failed {url}: {e}"
        log_with_timestamp(error_msg, "ERROR")
        raise RuntimeError(error_msg)

def estimate_ego_motion_from_pointclouds(path1, path2):
    """从点云估计自车运动"""
    try:
        log_with_timestamp("=== Starting ego motion estimation from point clouds ===")

        # 加载点云
        pcd1 = o3d.io.read_point_cloud(path1)
        pcd2 = o3d.io.read_point_cloud(path2)

        log_with_timestamp(f"Loaded point clouds - PCD1: {len(pcd1.points)} points, PCD2: {len(pcd2.points)} points")

        if len(pcd1.points) == 0 or len(pcd2.points) == 0:
            log_with_timestamp("Empty point cloud detected, using identity matrix", "WARNING")
            return np.eye(4)

        # 移除离群点
        pcd1, _ = pcd1.remove_statistical_outlier(nb_neighbors=20, std_ratio=2.0)
        pcd2, _ = pcd2.remove_statistical_outlier(nb_neighbors=20, std_ratio=2.0)
        log_with_timestamp(f"After outlier removal - PCD1: {len(pcd1.points)} points, PCD2: {len(pcd2.points)} points")

        # 自适应下采样
        # 根据点云大小选择合适的体素大小
        if len(pcd1.points) > 100000:
            voxel_size = 0.5
        elif len(pcd1.points) > 50000:
            voxel_size = 0.3
        else:
            voxel_size = 0.2

        log_with_timestamp(f"Using voxel size: {voxel_size}")

        pcd1_down = pcd1.voxel_down_sample(voxel_size)
        pcd2_down = pcd2.voxel_down_sample(voxel_size)

        log_with_timestamp(f"After downsampling - PCD1: {len(pcd1_down.points)} points, PCD2: {len(pcd2_down.points)} points")

        if len(pcd1_down.points) < 100 or len(pcd2_down.points) < 100:
            log_with_timestamp("Too few points after downsampling, using identity matrix", "WARNING")
            return np.eye(4)

        # 估计法线
        log_with_timestamp("Computing normals...")
        radius_normal = voxel_size * 2
        pcd1_down.estimate_normals(o3d.geometry.KDTreeSearchParamHybrid(radius=radius_normal, max_nn=30))
        pcd2_down.estimate_normals(o3d.geometry.KDTreeSearchParamHybrid(radius=radius_normal, max_nn=30))

        # 计算FPFH特征
        log_with_timestamp("Computing FPFH features...")
        radius_feature = voxel_size * 5
        fpfh1 = o3d.pipelines.registration.compute_fpfh_feature(
            pcd1_down, o3d.geometry.KDTreeSearchParamHybrid(radius=radius_feature, max_nn=100))
        fpfh2 = o3d.pipelines.registration.compute_fpfh_feature(
            pcd2_down, o3d.geometry.KDTreeSearchParamHybrid(radius=radius_feature, max_nn=100))

        log_with_timestamp(f"FPFH features computed - PCD1: {fpfh1.data.shape[1]} features, PCD2: {fpfh2.data.shape[1]} features")

        # 全局配准
        log_with_timestamp("Performing global registration...")
        distance_threshold = voxel_size * 1.5

        result = o3d.pipelines.registration.registration_ransac_based_on_feature_matching(
            pcd1_down, pcd2_down, fpfh1, fpfh2, True, distance_threshold,
            o3d.pipelines.registration.TransformationEstimationPointToPoint(False), 3,
            [o3d.pipelines.registration.CorrespondenceCheckerBasedOnEdgeLength(0.9),
             o3d.pipelines.registration.CorrespondenceCheckerBasedOnDistance(distance_threshold)],
            o3d.pipelines.registration.RANSACConvergenceCriteria(100000, 0.999))

        log_with_timestamp(f"Global registration completed - Fitness: {result.fitness:.4f}, RMSE: {result.inlier_rmse:.4f}")

        if result.fitness < 0.1:
            log_with_timestamp(f"Global registration fitness too low ({result.fitness:.4f}), using identity matrix", "WARNING")
            return np.eye(4)

        # ICP精配准
        log_with_timestamp("Performing ICP refinement...")
        refined_result = o3d.pipelines.registration.registration_icp(
            pcd1_down, pcd2_down, distance_threshold, result.transformation,
            o3d.pipelines.registration.TransformationEstimationPointToPoint())

        log_with_timestamp(f"ICP refinement completed - Fitness: {refined_result.fitness:.4f}, RMSE: {refined_result.inlier_rmse:.4f}")

        # 验证变换的合理性
        ego_transform = refined_result.transformation
        translation = ego_transform[:3, 3]
        rotation_matrix = ego_transform[:3, :3]

        # 计算旋转角度
        from scipy.spatial.transform import Rotation as R
        rotation_euler = R.from_matrix(rotation_matrix).as_euler('xyz')

        translation_norm = np.linalg.norm(translation)
        rotation_norm = np.linalg.norm(rotation_euler)

        log_with_timestamp(f"Estimated ego motion:")
        log_with_timestamp(f"  Translation: [{translation[0]:.4f}, {translation[1]:.4f}, {translation[2]:.4f}] (norm: {translation_norm:.4f}m)")
        log_with_timestamp(f"  Rotation: [{rotation_euler[0]:.4f}, {rotation_euler[1]:.4f}, {rotation_euler[2]:.4f}] (norm: {rotation_norm:.4f}rad)")

        # 合理性检查
        if translation_norm > 10.0:  # 平移超过10米
            log_with_timestamp(f"Translation too large ({translation_norm:.2f}m), using identity matrix", "WARNING")
            return np.eye(4)

        if rotation_norm > 1.0:  # 旋转超过57度
            log_with_timestamp(f"Rotation too large ({np.degrees(rotation_norm):.1f} degrees), using identity matrix", "WARNING")
            return np.eye(4)

        log_with_timestamp("Ego motion estimation successful")
        return ego_transform

    except Exception as e:
        log_with_timestamp(f"Failed to estimate ego motion: {e}", "ERROR")
        log_with_timestamp(f"Error details: {traceback.format_exc()}", "ERROR")
        log_with_timestamp("Falling back to identity matrix (no motion)", "WARNING")
        return np.eye(4)

def process_static_object(path1, path2, bbox_center, bbox_size, bbox_rotation, ego_transform, obj_id):
    """处理静止物体"""

    log_with_timestamp("=== Processing static object ===", obj_id=obj_id)
    log_vector(bbox_center, "Input center", obj_id)
    log_vector(bbox_size, "Input size", obj_id)
    log_vector(bbox_rotation, "Input rotation", obj_id)

    start_time = time.time()

    try:
        # 如果没有自车变换，尝试从点云估计
        if ego_transform is None:
            log_with_timestamp("No ego transform provided, estimating from point clouds", obj_id=obj_id)
            ego_transform = estimate_ego_motion_from_pointclouds(path1, path2)
        
        log_matrix(ego_transform, "Ego transform matrix", obj_id)
        
        # 计算自车运动补偿
        log_with_timestamp("Computing ego motion compensation", obj_id=obj_id)
        
        # 计算逆变换矩阵
        ego_transform_inv = np.linalg.inv(ego_transform)
        log_matrix(ego_transform_inv, "Ego inverse transform", obj_id)
        
        # 应用逆变换到物体位置
        bbox_center_homo = np.append(bbox_center, 1.0)
        log_vector(bbox_center_homo, "Original center homogeneous", obj_id)
        
        new_center_homo = np.dot(ego_transform_inv, bbox_center_homo)
        log_vector(new_center_homo, "Transformed center homogeneous", obj_id)
        
        new_center = new_center_homo[:3]
        
        log_vector(bbox_center, "Original center", obj_id)
        log_vector(new_center, "Transformed center", obj_id)
        
        position_change = new_center - np.array(bbox_center)
        log_vector(position_change, "Position change", obj_id)
        log_with_timestamp(f"Position change distance: {np.linalg.norm(position_change):.4f}m", obj_id=obj_id)
        
        # 旋转变换
        log_with_timestamp("Computing rotation transformation", obj_id=obj_id)
        ego_rotation = ego_transform_inv[:3, :3]
        log_matrix(ego_rotation, "Ego rotation inverse", obj_id)
        
        original_rotation_matrix = R.from_euler('xyz', bbox_rotation).as_matrix()
        log_matrix(original_rotation_matrix, "Original rotation matrix", obj_id)
        
        new_rotation_matrix = np.dot(ego_rotation, original_rotation_matrix)
        log_matrix(new_rotation_matrix, "Transformed rotation matrix", obj_id)
        
        new_euler = R.from_matrix(new_rotation_matrix).as_euler('xyz')
        
        log_vector(bbox_rotation, "Original rotation", obj_id)
        log_vector(new_euler, "Transformed rotation", obj_id)
        
        rotation_change = new_euler - np.array(bbox_rotation)
        log_vector(rotation_change, "Rotation change", obj_id)
        
        # 构建结果
        result = {
            "size3D": {
                "x": float(bbox_size[0]),
                "y": float(bbox_size[1]),
                "z": float(bbox_size[2]),
            },
            "center3D": {
                "x": float(new_center[0]),
                "y": float(new_center[1]),
                "z": float(new_center[2]),
            },
            "rotation3D": {
                "x": float(new_euler[0]),
                "y": float(new_euler[1]),
                "z": float(new_euler[2]),
            },
            "points": 0,  # Simplified version doesn't count points
            "is_stationary": True,
            "ego_compensated": True
        }
        
        processing_time = time.time() - start_time
        log_with_timestamp(f"Static object processing completed in {processing_time:.3f}s", obj_id=obj_id)
        
        # Final verification
        final_position_change = np.array([result["center3D"]["x"], result["center3D"]["y"], result["center3D"]["z"]]) - np.array(bbox_center)
        log_vector(final_position_change, "Final position change", obj_id)
        log_with_timestamp(f"Final position change distance: {np.linalg.norm(final_position_change):.4f}m", obj_id=obj_id)
        
        return result
        
    except Exception as e:
        log_with_timestamp(f"Static object processing failed: {e}", "ERROR", obj_id)
        log_with_timestamp(f"Error details: {traceback.format_exc()}", "ERROR", obj_id)
        raise

@app.route('/predict', methods=['POST'])
def predict():
    global current_request_id
    current_request_id = str(uuid.uuid4())
    
    log_with_timestamp("=== New prediction request (static object version) ===")
    
    try:
        data = request.get_json()
        log_with_timestamp(f"Received request data: {json.dumps(data, indent=2)}")

        # Get parameters
        path1 = data.get('pcd1Url')
        path2 = data.get('pcd2Url')
        objects = data.get('objects', [])
        
        log_with_timestamp(f"Point cloud file 1: {path1}")
        log_with_timestamp(f"Point cloud file 2: {path2}")
        log_with_timestamp(f"Number of objects: {len(objects)}")
        
        # Get ego pose information
        ego_pose_data = data.get('egoPose', None)
        ego_transform = None
        if ego_pose_data:
            log_with_timestamp(f"Ego pose data: {json.dumps(ego_pose_data, indent=2)}")
            ego_transform = parse_ego_pose(ego_pose_data)
            if ego_transform is not None:
                log_matrix(ego_transform, "Parsed ego transform matrix")
            else:
                log_with_timestamp("Ego pose parsing failed", "ERROR")
        
        # Create temporary directory and download files
        with tempfile.TemporaryDirectory() as temp_dir:
            download_start_time = time.time()
            log_with_timestamp("Starting point cloud file download")
            
            path1 = download_file_from_url(path1, temp_dir)
            path2 = download_file_from_url(path2, temp_dir)
            
            download_end_time = time.time()
            download_duration = download_end_time - download_start_time
            log_with_timestamp(f"File download completed in {download_duration:.3f}s")
            
            # Process all objects (treat all as static objects)
            results = []
            processing_start_time = time.time()
            
            for obj in objects:
                obj_id = obj.get("objectId", "unknown")
                size3D = obj.get("size3D", {})
                center3D = obj.get("center3D", {})
                rotation3D = obj.get("rotation3D", {})

                bbox_center = [center3D.get("x", 0), center3D.get("y", 0), center3D.get("z", 0)]
                bbox_size = [size3D.get("x", 1), size3D.get("y", 1), size3D.get("z", 1)]
                bbox_rotation = [rotation3D.get("x", 0), rotation3D.get("y", 0), rotation3D.get("z", 0)]
                
                try:
                    result = process_static_object(path1, path2, bbox_center, bbox_size, bbox_rotation, ego_transform, obj_id)
                    result["objectId"] = obj_id
                    results.append(result)
                    
                except Exception as e:
                    log_with_timestamp(f"Object {obj_id} processing failed: {e}", "ERROR", obj_id)
                    # Return original position as fallback
                    result = {
                        "objectId": obj_id,
                        "size3D": size3D,
                        "center3D": center3D,
                        "rotation3D": rotation3D,
                        "points": 0,
                        "error": str(e)
                    }
                    results.append(result)
            
            processing_end_time = time.time()
            processing_duration = processing_end_time - processing_start_time
            log_with_timestamp(f"All objects processed in {processing_duration:.3f}s")
            
            # Statistics
            success_count = sum(1 for r in results if "error" not in r)
            error_count = len(results) - success_count
            
            log_with_timestamp(f"Processing statistics - Success: {success_count}, Errors: {error_count}")
            
            total_duration = processing_end_time - download_start_time
            log_with_timestamp(f"=== Prediction request completed in {total_duration:.3f}s ===")
            
            return jsonify({
                "status": "success",
                "result": results,
                "request_id": current_request_id,
                "processing_time": {
                    "download": download_duration,
                    "processing": processing_duration,
                    "total": total_duration
                },
                "statistics": {
                    "total_objects": len(objects),
                    "success_objects": success_count,
                    "error_objects": error_count
                },
                "timestamp": datetime.now().isoformat(),
                "version": "fixed_static_only"
            })
            
    except Exception as e:
        error_msg = f"Error during processing: {str(e)}"
        log_with_timestamp(error_msg, "ERROR")
        log_with_timestamp(f"Error details: {traceback.format_exc()}", "ERROR")
        
        return jsonify({
            "status": "fail",
            "result": error_msg,
            "request_id": current_request_id,
            "timestamp": datetime.now().isoformat()
        })

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5002, debug=True)
