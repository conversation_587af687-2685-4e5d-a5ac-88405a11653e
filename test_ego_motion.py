#!/usr/bin/env python3
"""
测试自车运动补偿功能
"""

import numpy as np
from scipy.spatial.transform import Rotation as R

def test_ego_motion_compensation():
    """测试自车运动补偿的逻辑"""
    
    # 模拟场景：自车向前移动1米，向右转5度
    ego_translation = np.array([1.0, 0.0, 0.0])  # 向前1米
    ego_rotation = np.array([0.0, 0.0, np.radians(5)])  # 向右转5度
    
    # 构建自车变换矩阵
    ego_rotation_matrix = R.from_euler('xyz', ego_rotation).as_matrix()
    ego_transform = np.eye(4)
    ego_transform[:3, :3] = ego_rotation_matrix
    ego_transform[:3, 3] = ego_translation
    
    print("自车变换矩阵:")
    print(ego_transform)
    
    # 模拟静止物体在第一帧的位置（相对于自车）
    object_center_frame1 = np.array([5.0, 2.0, 0.0])  # 前方5米，右侧2米
    object_rotation_frame1 = np.array([0.0, 0.0, 0.0])  # 无旋转
    
    print(f"\n物体在第一帧的位置: {object_center_frame1}")
    
    # 计算物体在第二帧的预期位置（考虑自车运动）
    # 对于静止物体，应用自车运动的逆变换
    ego_transform_inv = np.linalg.inv(ego_transform)
    
    object_center_homo = np.append(object_center_frame1, 1.0)
    object_center_frame2_homo = np.dot(ego_transform_inv, object_center_homo)
    object_center_frame2 = object_center_frame2_homo[:3]
    
    print(f"物体在第二帧的预期位置: {object_center_frame2}")
    print(f"位置变化: {object_center_frame2 - object_center_frame1}")
    
    # 验证：如果自车向前移动1米，静止物体相对位置应该向后移动1米
    expected_change = np.array([-1.0, 0.0, 0.0])  # 相对位置向后
    actual_change = object_center_frame2 - object_center_frame1
    
    print(f"\n预期变化: {expected_change}")
    print(f"实际变化: {actual_change}")
    print(f"误差: {np.linalg.norm(actual_change - expected_change)}")

def test_api_format():
    """测试API数据格式"""
    
    # 示例API请求数据
    api_data = {
        "pcd1Url": "path/to/frame1.pcd",
        "pcd2Url": "path/to/frame2.pcd",
        "egoPose": {
            "translation": [1.0, 0.0, 0.0],  # 向前移动1米
            "rotation": [0.0, 0.0, 0.087]     # 向右转5度（弧度）
        },
        "objects": [
            {
                "objectId": "obj_001",
                "center3D": {"x": 5.0, "y": 2.0, "z": 0.0},
                "size3D": {"x": 2.0, "y": 1.0, "z": 1.5},
                "rotation3D": {"x": 0.0, "y": 0.0, "z": 0.0}
            }
        ]
    }
    
    print("示例API请求格式:")
    import json
    print(json.dumps(api_data, indent=2))

if __name__ == "__main__":
    print("=== 测试自车运动补偿逻辑 ===")
    test_ego_motion_compensation()
    
    print("\n=== 测试API数据格式 ===")
    test_api_format()
