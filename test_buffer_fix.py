#!/usr/bin/env python3
"""
测试修复后的缓冲区问题
"""

import numpy as np
from scipy.spatial.transform import Rotation as R

def test_rotation_matrix_fix():
    """测试旋转矩阵的只读缓冲区修复"""
    
    print("=== 测试旋转矩阵只读缓冲区修复 ===")
    
    # 模拟Open3D返回的只读变换矩阵
    transform_matrix = np.array([
        [0.9962, -0.0872, 0.0000, 1.0000],
        [0.0872,  0.9962, 0.0000, 0.0000],
        [0.0000,  0.0000, 1.0000, 0.0000],
        [0.0000,  0.0000, 0.0000, 1.0000]
    ])
    
    # 设置为只读（模拟Open3D的行为）
    transform_matrix.flags.writeable = False
    print(f"变换矩阵是否只读: {not transform_matrix.flags.writeable}")
    
    # 提取旋转部分
    rotation_matrix = transform_matrix[:3, :3]
    print(f"旋转矩阵是否只读: {not rotation_matrix.flags.writeable}")
    
    try:
        # 尝试直接使用（这会失败）
        print("尝试直接使用只读矩阵...")
        rotation_euler_direct = R.from_matrix(rotation_matrix).as_euler('xyz')
        print("❌ 这不应该成功")
    except ValueError as e:
        print(f"✅ 预期的错误: {e}")
    
    try:
        # 使用修复方法
        print("使用修复方法（创建可写副本）...")
        rotation_matrix_copy = np.array(rotation_matrix, copy=True)
        print(f"副本是否可写: {rotation_matrix_copy.flags.writeable}")
        rotation_euler_fixed = R.from_matrix(rotation_matrix_copy).as_euler('xyz')
        print(f"✅ 修复成功! 旋转角度: {rotation_euler_fixed}")
        print(f"旋转角度（度）: {np.degrees(rotation_euler_fixed)}")
    except Exception as e:
        print(f"❌ 修复失败: {e}")

def test_ego_motion_calculation():
    """测试完整的自车运动计算"""
    
    print(f"\n=== 测试完整的自车运动计算 ===")
    
    # 模拟一个合理的自车变换
    ego_translation = np.array([0.8, -0.1, 0.0])  # 向前0.8米，向左0.1米
    ego_rotation_euler = np.array([0.0, 0.0, 0.05])  # 向右转约3度
    
    # 构建变换矩阵
    ego_rotation_matrix = R.from_euler('xyz', ego_rotation_euler).as_matrix()
    ego_transform = np.eye(4)
    ego_transform[:3, :3] = ego_rotation_matrix
    ego_transform[:3, 3] = ego_translation
    
    # 设置为只读
    ego_transform.flags.writeable = False
    
    print(f"模拟的自车变换矩阵:")
    for i, row in enumerate(ego_transform):
        print(f"  [{i}] {row}")
    
    # 提取并验证
    translation = ego_transform[:3, 3]
    rotation_matrix = ego_transform[:3, :3]
    
    # 使用修复方法
    rotation_matrix_copy = np.array(rotation_matrix, copy=True)
    rotation_euler = R.from_matrix(rotation_matrix_copy).as_euler('xyz')
    
    translation_norm = np.linalg.norm(translation)
    rotation_norm = np.linalg.norm(rotation_euler)
    
    print(f"提取的运动信息:")
    print(f"  平移: {translation} (距离: {translation_norm:.4f}m)")
    print(f"  旋转: {rotation_euler} (角度: {rotation_norm:.4f}rad = {np.degrees(rotation_norm):.1f}度)")
    
    # 合理性检查
    if translation_norm > 10.0:
        print(f"❌ 平移过大: {translation_norm:.2f}m")
    else:
        print(f"✅ 平移合理: {translation_norm:.2f}m")
        
    if rotation_norm > 1.0:
        print(f"❌ 旋转过大: {np.degrees(rotation_norm):.1f}度")
    else:
        print(f"✅ 旋转合理: {np.degrees(rotation_norm):.1f}度")

def test_static_object_compensation():
    """测试静止物体补偿"""
    
    print(f"\n=== 测试静止物体补偿 ===")
    
    # 自车运动：向前1米，向右转5度
    ego_translation = np.array([1.0, 0.0, 0.0])
    ego_rotation_euler = np.array([0.0, 0.0, np.radians(5)])
    
    ego_rotation_matrix = R.from_euler('xyz', ego_rotation_euler).as_matrix()
    ego_transform = np.eye(4)
    ego_transform[:3, :3] = ego_rotation_matrix
    ego_transform[:3, 3] = ego_translation
    
    print(f"自车变换矩阵:")
    for i, row in enumerate(ego_transform):
        print(f"  [{i}] {row}")
    
    # 静止物体原始位置
    original_center = np.array([14.3054, 2.9938, 3.9605])
    
    # 应用逆变换
    ego_transform_inv = np.linalg.inv(ego_transform)
    original_center_homo = np.append(original_center, 1.0)
    new_center_homo = np.dot(ego_transform_inv, original_center_homo)
    new_center = new_center_homo[:3]
    
    position_change = new_center - original_center
    
    print(f"静止物体补偿:")
    print(f"  原始位置: {original_center}")
    print(f"  新位置: {new_center}")
    print(f"  位置变化: {position_change}")
    print(f"  变化距离: {np.linalg.norm(position_change):.4f}m")
    
    if np.linalg.norm(position_change) > 0.1:
        print(f"✅ 检测到位置变化，自车运动补偿正常工作")
    else:
        print(f"❌ 没有位置变化，可能存在问题")

if __name__ == "__main__":
    test_rotation_matrix_fix()
    test_ego_motion_calculation()
    test_static_object_compensation()
    
    print(f"\n=== 修复总结 ===")
    print("✅ 修复了scipy旋转矩阵的只读缓冲区问题")
    print("✅ 使用np.array(matrix, copy=True)创建可写副本")
    print("✅ 保持了原有的功能逻辑")
    print("✅ 添加了详细的错误处理")
    
    print(f"\n=== 下一步 ===")
    print("1. 重启app_fixed.py服务器")
    print("2. 重新发送API请求（不包含egoPose）")
    print("3. 观察是否能成功估计自车运动")
    print("4. 检查静止物体位置是否有变化")
