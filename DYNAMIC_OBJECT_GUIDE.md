# 动态物体处理指南

## 功能概述

现在系统支持处理两种类型的物体：
- ✅ **静止物体**：只需要补偿自车运动
- ✅ **动态物体**：需要估计物体运动 + 补偿自车运动

## API格式

### 静止物体（默认）
```json
{
  "objects": [
    {
      "objectId": "static_car_1",
      "center3D": {"x": 14.3054, "y": 2.9938, "z": 3.9605},
      "size3D": {"x": 6.7784, "y": 3.0099, "z": 7.9209},
      "rotation3D": {"x": 0.0, "y": 0.0, "z": -0.192},
      "is_static": true  // 可选，默认为true
    }
  ]
}
```

### 动态物体
```json
{
  "objects": [
    {
      "objectId": "moving_car_1",
      "center3D": {"x": 20.5, "y": -5.2, "z": 3.8},
      "size3D": {"x": 4.5, "y": 2.0, "z": 1.8},
      "rotation3D": {"x": 0.0, "y": 0.0, "z": 1.57},
      "is_static": false,
      "velocity": [5.0, 2.0, 0.0]  // 速度向量 [vx, vy, vz] m/s
    }
  ]
}
```

### 混合场景
```json
{
  "objects": [
    {
      "objectId": "static_building",
      "center3D": {"x": 50.0, "y": 10.0, "z": 5.0},
      "size3D": {"x": 20.0, "y": 15.0, "z": 10.0},
      "rotation3D": {"x": 0.0, "y": 0.0, "z": 0.0},
      "is_static": true
    },
    {
      "objectId": "moving_pedestrian",
      "center3D": {"x": 15.0, "y": 2.0, "z": 1.0},
      "size3D": {"x": 0.6, "y": 0.6, "z": 1.8},
      "rotation3D": {"x": 0.0, "y": 0.0, "z": 0.5},
      "is_static": false,
      "velocity": [1.2, 0.8, 0.0]
    }
  ]
}
```

## 处理逻辑

### 静止物体
1. **自车运动补偿**：使用ICP估计的自车运动
2. **位置补偿**：`new_position = ego_transform @ original_position`
3. **旋转补偿**：`new_rotation = ego_rotation @ original_rotation`

### 动态物体
1. **物体运动预测**：`predicted_position = original_position + velocity * dt`
2. **自车运动补偿**：`final_position = ego_transform @ predicted_position`
3. **旋转补偿**：同时考虑物体旋转和自车旋转

## 参数说明

### velocity（速度向量）
- **格式**：`[vx, vy, vz]`，单位：米/秒
- **坐标系**：与点云坐标系一致
- **示例**：
  - `[5.0, 0.0, 0.0]`：向前5m/s
  - `[0.0, 2.0, 0.0]`：向左2m/s
  - `[-3.0, 1.0, 0.0]`：向后3m/s，向左1m/s

### is_static（物体类型）
- **true**：静止物体（建筑、停放的车辆等）
- **false**：动态物体（行驶的车辆、行人等）

### 时间间隔（dt）
- **默认值**：0.1秒（100ms）
- **含义**：两帧点云之间的时间间隔
- **可调整**：可以根据实际采集频率调整

## API响应

### 静止物体响应
```json
{
  "objectId": "static_car_1",
  "center3D": {"x": 13.715, "y": 1.805, "z": 3.961},
  "rotation3D": {"x": 0.02, "y": 0.01, "z": -0.15},
  "object_type": "static",
  "velocity": [0.0, 0.0, 0.0],
  "compensation_test": {
    "method_A_direct": {...},
    "method_B_inverse": {...},
    "chosen_method": "A (direct ICP)"
  }
}
```

### 动态物体响应
```json
{
  "objectId": "moving_car_1",
  "center3D": {"x": 21.2, "y": -4.8, "z": 3.8},
  "rotation3D": {"x": 0.01, "y": 0.02, "z": 1.55},
  "object_type": "dynamic",
  "velocity": [5.0, 2.0, 0.0],
  "compensation_test": {
    "method_A_direct": {...},
    "method_B_inverse": {...},
    "chosen_method": "A (direct ICP)"
  }
}
```

## 日志输出

### 静止物体日志
```
=== Processing STATIC object ===
Processing as STATIC object - ego motion compensation only
Method A (direct ICP):
  New position: [13.715, 1.805, 3.961]
  Position change: [-0.590, -1.188, 0.000]
  New rotation: [0.02, 0.01, -0.15]
  Rotation change: [0.02, 0.01, 0.04]
```

### 动态物体日志
```
=== Processing DYNAMIC object ===
Input velocity: [5.0000, 2.0000, 0.0000]
Estimated velocity: [5.0000, 2.0000, 0.0000]
Object motion (velocity * dt): [0.5000, 0.2000, 0.0000]
Predicted center (after object motion): [21.0000, -5.0000, 3.8000]
Processing as DYNAMIC object - object motion + ego compensation
Method A (direct ICP):
  New position: [21.2, -4.8, 3.8]
  Position change: [0.7, 0.2, 0.0]
```

## 使用场景

### 1. 自动驾驶
- **静止物体**：路边停放的车辆、建筑物、交通标志
- **动态物体**：行驶中的车辆、行人、自行车

### 2. 机器人导航
- **静止物体**：墙壁、家具、固定障碍物
- **动态物体**：移动的人、宠物、其他机器人

### 3. 工业检测
- **静止物体**：设备、结构件
- **动态物体**：传送带上的物品、移动设备

## 高级功能

### 1. 速度估计
如果没有提供速度，系统会：
- 发出警告：`No velocity provided, assuming stationary`
- 使用默认速度：`[0.0, 0.0, 0.0]`
- 可以扩展为从历史轨迹估计速度

### 2. 运动模型
当前使用简单的匀速直线运动模型：
- `new_position = old_position + velocity * dt`
- 可以扩展为更复杂的运动模型（加速度、转弯等）

### 3. 不确定性处理
- 可以添加速度和位置的不确定性估计
- 支持概率性的运动预测

## 测试建议

### 1. 静止物体测试
```bash
# 使用现有的静止物体数据
# 应该看到与之前相同的结果
```

### 2. 动态物体测试
```bash
# 添加速度信息测试动态物体
# 观察位置变化是否合理
```

### 3. 混合场景测试
```bash
# 同时包含静止和动态物体
# 验证不同类型物体的处理是否正确
```

现在系统可以智能地处理静止和动态物体了！
