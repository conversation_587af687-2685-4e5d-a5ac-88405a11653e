# 左转方向修复指南

## 问题确认

您观察到：
- **实际情况**：自车左转
- **当前算法**：预测物体位置偏右
- **问题**：旋转方向可能相反

## 解决方案

我创建了一个**方向测试版本** `app_direction_test.py`，它会同时输出两个方向的结果，让您选择正确的方向。

### 1. 测试版本特性

**端口5003**，同时测试两个方向：
- **方向1**：使用ICP逆变换（当前方法）
- **方向2**：直接使用ICP变换

### 2. 输出格式

API响应会包含 `direction_test` 字段：

```json
{
  "result": [
    {
      "objectId": "test_obj",
      "center3D": {"x": 13.715, "y": 1.805, "z": 3.961},  // 默认使用方向1
      "direction_test": {
        "direction_1": {
          "center": [13.715, 1.805, 3.961],
          "change": [-0.590, -1.188, 0.000],
          "y_direction": "left"
        },
        "direction_2": {
          "center": [14.790, 4.229, 3.961],
          "change": [0.485, 1.235, 0.000],
          "y_direction": "right"
        }
      }
    }
  ]
}
```

### 3. 使用方法

#### 启动测试服务器
```bash
python app_direction_test.py  # 端口5003
```

#### 发送API请求
```json
{
  "pcd1Url": "path/to/your/frame1.pcd",
  "pcd2Url": "path/to/your/frame2.pcd",
  "objects": [
    {
      "objectId": "left_turn_test",
      "center3D": {"x": 14.3054, "y": 2.9938, "z": 3.9605},
      "size3D": {"x": 6.7784, "y": 3.0099, "z": 7.9209},
      "rotation3D": {"x": 0.0, "y": 0.0, "z": -0.192}
    }
  ]
}
```

#### 分析结果
观察 `direction_test` 中的两个方向：

**对于自车左转，静止物体应该向右偏移**：
- 如果 `direction_1.y_direction` 是 "right" → 使用方向1 ✅
- 如果 `direction_2.y_direction` 是 "right" → 使用方向2 ✅

### 4. 预期结果分析

#### 自车左转时的物理预期
```
自车视角：
    ↑ 前方
    |
左 ←+→ 右
    |
    ↓ 后方

自车左转 → 静止物体相对向右偏移
```

#### 坐标系对应
- **X轴**：通常是前方方向
- **Y轴**：左右方向（正值可能是左或右，取决于坐标系定义）
- **Z轴**：上下方向

### 5. 确定正确方向

请运行测试版本，然后告诉我：

1. **方向1的结果**：
   - Y变化值：`direction_1.change[1]`
   - 方向：`direction_1.y_direction`

2. **方向2的结果**：
   - Y变化值：`direction_2.change[1]`
   - 方向：`direction_2.y_direction`

3. **您的观察**：
   - 哪个方向的结果符合您看到的实际情况？
   - 自车左转时，物体确实向哪个方向偏移了？

### 6. 最终修复

确定正确方向后，我会修改 `app_fixed.py`：

#### 如果方向1正确（当前默认）
```python
ego_transform = np.linalg.inv(icp_transform)  # 使用逆变换
```

#### 如果方向2正确
```python
ego_transform = icp_transform  # 直接使用ICP结果
```

### 7. 日志分析

测试版本会输出详细日志：
```
=== Direction Test Results ===
Direction 1 (using ICP inverse):
  New position: [13.715, 1.805, 3.961]
  Position change: [-0.590, -1.188, 0.000]
  Y change: -1.1885 (left)

Direction 2 (using ICP direct):
  New position: [14.790, 4.229, 3.961]
  Position change: [0.485, 1.235, 0.000]
  Y change: 1.2354 (right)
```

### 8. 坐标系验证

如果两个方向都不对，可能需要检查：
- Y轴的正方向定义
- 坐标系是否为左手系/右手系
- 是否需要额外的坐标变换

## 下一步

1. **运行测试版本**：`python app_direction_test.py`
2. **发送API请求**：使用您的真实点云数据
3. **分析结果**：查看两个方向的输出
4. **告诉我结果**：哪个方向符合实际观察
5. **应用修复**：我会相应修改主算法

现在请测试并告诉我哪个方向是正确的！
