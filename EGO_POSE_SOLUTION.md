# 自车位姿问题解决方案

## 问题诊断

从您的日志可以清楚看到问题：

```
No ego transform provided, assuming no ego motion
Ego transform matrix: [单位矩阵]
Position change: [0.0000, 0.0000, 0.0000]
```

**根本原因**: 您的API请求中**没有提供 `egoPose` 参数**，所以算法假设自车没有运动，使用单位矩阵，导致静止物体位置完全不变。

## 解决方案

### 1. 提供自车位姿信息

在API请求中添加 `egoPose` 参数：

```json
{
  "pcd1Url": "path/to/frame1.pcd",
  "pcd2Url": "path/to/frame2.pcd",
  "egoPose": {
    "translation": [1.0, 0.0, 0.0],  // 自车向前移动1米
    "rotation": [0.0, 0.0, 0.087]     // 自车向右转5度（弧度）
  },
  "objects": [...]
}
```

### 2. 支持的自车位姿格式

#### 格式1：平移+旋转（推荐）
```json
"egoPose": {
  "translation": [x, y, z],        // 平移向量（米）
  "rotation": [rx, ry, rz]         // 旋转角度（弧度）
}
```

#### 格式2：4x4变换矩阵
```json
"egoPose": {
  "transform_matrix": [
    [r11, r12, r13, tx],
    [r21, r22, r23, ty], 
    [r31, r32, r33, tz],
    [0,   0,   0,   1]
  ]
}
```

### 3. 修复后的效果

**您的测试案例**：
- **输入位置**: [14.3054, 2.9938, 3.9605]
- **自车运动**: 向前1米，向右转5度
- **预期输出**: [13.516, 1.823, 3.9605]
- **位置变化**: [-0.790, -1.171, 0.0]，距离1.41米

这是**正确的**！静止物体相对于自车的位置应该：
- 向后偏移（因为自车向前）
- 向左偏移（因为自车向右转）

## 测试方法

### 1. 使用测试脚本
```bash
python test_with_ego_pose.py
```

### 2. 使用curl命令
```bash
curl -X POST http://localhost:5002/predict \
  -H 'Content-Type: application/json' \
  -d '{
    "pcd1Url": "https://example.com/frame1.pcd",
    "pcd2Url": "https://example.com/frame2.pcd", 
    "egoPose": {
      "translation": [1.0, 0.0, 0.0],
      "rotation": [0.0, 0.0, 0.087]
    },
    "objects": [
      {
        "objectId": "test_obj",
        "center3D": {"x": 14.3054, "y": 2.9938, "z": 3.9605},
        "size3D": {"x": 6.7784, "y": 3.0099, "z": 7.9209},
        "rotation3D": {"x": 0.0, "y": 0.0, "z": -0.192}
      }
    ]
  }'
```

### 3. 启动服务器
```bash
python app_fixed.py  # 端口5002
```

## 自动估计功能

如果您无法提供精确的自车位姿，修复版本会尝试从点云自动估计：

```json
{
  "pcd1Url": "path/to/frame1.pcd",
  "pcd2Url": "path/to/frame2.pcd",
  // 不提供egoPose，算法会自动估计
  "objects": [...]
}
```

算法会：
1. 加载两帧点云
2. 下采样提高速度
3. 计算FPFH特征
4. 执行全局配准
5. ICP精配准
6. 得到自车变换矩阵

## 预期日志输出

提供自车位姿后，您应该看到：

```
Ego transform matrix:
  [0] [  0.9962  -0.0872   0.0000   1.0000]
  [1] [  0.0872   0.9962   0.0000   0.0000]
  [2] [  0.0000   0.0000   1.0000   0.0000]
  [3] [  0.0000   0.0000   0.0000   1.0000]

Original center: [ 14.3054   2.9938   3.9605]
Transformed center: [ 13.5157   1.8228   3.9605]
Position change: [ -0.7897  -1.1710   0.0000]
Position change distance: 1.4124m
```

## 关键要点

1. **必须提供egoPose**: 否则算法假设无自车运动
2. **角度使用弧度**: rotation参数使用弧度而不是度数
3. **坐标系一致**: 确保自车位姿与点云坐标系一致
4. **验证结果**: 静止物体应该与自车运动方向相反移动

现在您知道问题所在了：**请在API请求中添加 `egoPose` 参数**！
