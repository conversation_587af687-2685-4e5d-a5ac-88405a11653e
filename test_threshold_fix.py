#!/usr/bin/env python3
"""
测试阈值修复效果
"""

import numpy as np

def test_old_vs_new_thresholds():
    """对比修复前后的阈值设置"""
    
    # 测试物体参数（来自日志）
    bbox_size = [4.2293, 2.0908, 1.5663]
    volume = bbox_size[0] * bbox_size[1] * bbox_size[2]
    
    print("=== 阈值对比分析 ===")
    print(f"物体大小: {bbox_size}")
    print(f"物体体积: {volume:.4f}")
    
    # 旧的阈值计算
    old_rotation_threshold = max(0.02, min(0.05, volume * 0.001))
    old_position_threshold = max(0.1, min(0.5, volume * 0.01))
    
    print(f"\n--- 修复前的阈值 ---")
    print(f"旋转阈值: {old_rotation_threshold:.4f}弧度 ({np.degrees(old_rotation_threshold):.1f}度)")
    print(f"位置阈值: {old_position_threshold:.3f}m")
    
    # 新的阈值计算
    print(f"\n--- 修复后的阈值 ---")
    
    # 静止物体
    static_rotation_threshold = max(0.05, min(0.1, volume * 0.002))
    static_position_threshold = max(0.2, min(1.0, volume * 0.02))
    
    print(f"静止物体:")
    print(f"  旋转阈值: {static_rotation_threshold:.4f}弧度 ({np.degrees(static_rotation_threshold):.1f}度)")
    print(f"  位置阈值: {static_position_threshold:.3f}m")
    
    # 运动物体
    moving_rotation_threshold = max(0.1, min(0.3, volume * 0.005))
    moving_position_threshold = max(0.5, min(2.0, volume * 0.05))
    
    print(f"运动物体:")
    print(f"  旋转阈值: {moving_rotation_threshold:.4f}弧度 ({np.degrees(moving_rotation_threshold):.1f}度)")
    print(f"  位置阈值: {moving_position_threshold:.3f}m")
    
    # 实际测试数据
    actual_rotation_change = 0.1888  # 弧度
    actual_position_change = 0.3201  # 米
    
    print(f"\n--- 实际变化值 ---")
    print(f"旋转变化: {actual_rotation_change:.4f}弧度 ({np.degrees(actual_rotation_change):.1f}度)")
    print(f"位置变化: {actual_position_change:.4f}m")
    
    print(f"\n--- 阈值检查结果 ---")
    
    # 旧阈值检查
    old_rotation_pass = actual_rotation_change <= old_rotation_threshold
    old_position_pass = actual_position_change <= old_position_threshold
    
    print(f"修复前:")
    print(f"  旋转检查: {'通过' if old_rotation_pass else '失败'} ({actual_rotation_change:.4f} vs {old_rotation_threshold:.4f})")
    print(f"  位置检查: {'通过' if old_position_pass else '失败'} ({actual_position_change:.4f} vs {old_position_threshold:.4f})")
    print(f"  最终结果: {'保留预测' if old_rotation_pass and old_position_pass else '回退原位'}")
    
    # 新阈值检查（运动物体）
    new_rotation_pass = actual_rotation_change <= moving_rotation_threshold
    new_position_pass = actual_position_change <= moving_position_threshold
    
    # 智能跳过检查
    skip_check = actual_position_change < 5.0 and actual_rotation_change < 1.0
    
    print(f"修复后（运动物体）:")
    print(f"  旋转检查: {'通过' if new_rotation_pass else '失败'} ({actual_rotation_change:.4f} vs {moving_rotation_threshold:.4f})")
    print(f"  位置检查: {'通过' if new_position_pass else '失败'} ({actual_position_change:.4f} vs {moving_position_threshold:.4f})")
    print(f"  智能跳过: {'是' if skip_check else '否'} (变化在合理范围内)")
    
    if skip_check:
        print(f"  最终结果: 保留预测 (跳过严格检查)")
    else:
        print(f"  最终结果: {'保留预测' if new_rotation_pass and new_position_pass else '回退原位'}")

def test_different_scenarios():
    """测试不同场景下的阈值表现"""
    
    print(f"\n=== 不同场景测试 ===")
    
    scenarios = [
        {
            "name": "小物体静止",
            "size": [1.0, 1.0, 1.0],
            "is_stationary": True,
            "rotation_change": 0.03,  # 1.7度
            "position_change": 0.15   # 15cm
        },
        {
            "name": "大物体静止", 
            "size": [5.0, 3.0, 2.0],
            "is_stationary": True,
            "rotation_change": 0.08,  # 4.6度
            "position_change": 0.8    # 80cm
        },
        {
            "name": "小物体运动",
            "size": [1.0, 1.0, 1.0], 
            "is_stationary": False,
            "rotation_change": 0.2,   # 11.5度
            "position_change": 1.5    # 1.5m
        },
        {
            "name": "大物体运动",
            "size": [4.0, 2.0, 1.5],
            "is_stationary": False,
            "rotation_change": 0.15,  # 8.6度
            "position_change": 0.8    # 80cm
        }
    ]
    
    for scenario in scenarios:
        print(f"\n--- {scenario['name']} ---")
        
        size = scenario['size']
        volume = size[0] * size[1] * size[2]
        is_stationary = scenario['is_stationary']
        
        if is_stationary:
            rotation_threshold = max(0.05, min(0.1, volume * 0.002))
            position_threshold = max(0.2, min(1.0, volume * 0.02))
        else:
            rotation_threshold = max(0.1, min(0.3, volume * 0.005))
            position_threshold = max(0.5, min(2.0, volume * 0.05))
        
        rotation_change = scenario['rotation_change']
        position_change = scenario['position_change']
        
        # 智能跳过检查（仅运动物体）
        skip_check = not is_stationary and position_change < 5.0 and rotation_change < 1.0
        
        rotation_pass = rotation_change <= rotation_threshold
        position_pass = position_change <= position_threshold
        
        print(f"  物体大小: {size}, 体积: {volume:.2f}")
        print(f"  阈值: 旋转{rotation_threshold:.3f}弧度, 位置{position_threshold:.2f}m")
        print(f"  变化: 旋转{rotation_change:.3f}弧度, 位置{position_change:.2f}m")
        print(f"  智能跳过: {'是' if skip_check else '否'}")
        
        if skip_check:
            result = "保留预测"
        else:
            result = "保留预测" if rotation_pass and position_pass else "回退原位"
        
        print(f"  结果: {result}")

if __name__ == "__main__":
    test_old_vs_new_thresholds()
    test_different_scenarios()
