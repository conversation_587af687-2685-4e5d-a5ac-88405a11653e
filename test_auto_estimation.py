#!/usr/bin/env python3
"""
测试自动估计自车位姿功能
"""

import requests
import json
import numpy as np

def test_auto_estimation():
    """测试自动估计自车位姿"""
    
    print("=== 测试自动估计自车位姿 ===")
    
    # 不提供egoPose，让算法自动估计
    test_data = {
        "pcd1Url": "https://example.com/frame1.pcd",  # 这会失败，但可以看到逻辑
        "pcd2Url": "https://example.com/frame2.pcd",
        # 注意：不提供egoPose参数
        "objects": [
            {
                "objectId": "auto_estimation_test",
                "center3D": {"x": 14.3054, "y": 2.9938, "z": 3.9605},
                "size3D": {"x": 6.7784, "y": 3.0099, "z": 7.9209},
                "rotation3D": {"x": 0.0000, "y": 0.0000, "z": -0.1920}
            }
        ]
    }
    
    print("测试数据（无egoPose）:")
    print(json.dumps(test_data, indent=2))
    
    print(f"\n=== 预期行为 ===")
    print("1. 算法检测到没有提供egoPose")
    print("2. 自动从点云估计自车运动")
    print("3. 执行以下步骤:")
    print("   - 加载两帧点云")
    print("   - 移除离群点")
    print("   - 自适应下采样")
    print("   - 计算法线和FPFH特征")
    print("   - 全局配准（RANSAC）")
    print("   - ICP精配准")
    print("   - 合理性检查")
    print("4. 使用估计的自车变换进行静止物体补偿")

def test_with_real_data():
    """使用真实数据测试（如果有的话）"""
    
    print(f"\n=== 真实数据测试示例 ===")
    
    # 如果您有真实的点云文件，可以使用这个格式
    real_test_data = {
        "pcd1Url": "path/to/your/frame1.pcd",  # 替换为您的实际文件路径
        "pcd2Url": "path/to/your/frame2.pcd",  # 替换为您的实际文件路径
        "objects": [
            {
                "objectId": "real_object_1",
                "center3D": {"x": 14.3054, "y": 2.9938, "z": 3.9605},
                "size3D": {"x": 6.7784, "y": 3.0099, "z": 7.9209},
                "rotation3D": {"x": 0.0000, "y": 0.0000, "z": -0.1920}
            }
        ]
    }
    
    print("真实数据测试格式:")
    print(json.dumps(real_test_data, indent=2))

def create_curl_command_auto():
    """生成自动估计的curl命令"""
    
    test_data = {
        "pcd1Url": "path/to/your/frame1.pcd",
        "pcd2Url": "path/to/your/frame2.pcd",
        "objects": [
            {
                "objectId": "curl_auto_test",
                "center3D": {"x": 14.3054, "y": 2.9938, "z": 3.9605},
                "size3D": {"x": 6.7784, "y": 3.0099, "z": 7.9209},
                "rotation3D": {"x": 0.0000, "y": 0.0000, "z": -0.1920}
            }
        ]
    }
    
    print(f"\n=== 自动估计Curl命令 ===")
    print("curl -X POST http://localhost:5002/predict \\")
    print("  -H 'Content-Type: application/json' \\")
    print(f"  -d '{json.dumps(test_data)}'")

def explain_estimation_process():
    """解释估计过程"""
    
    print(f"\n=== 自车位姿估计过程详解 ===")
    
    print("1. 点云预处理:")
    print("   - 移除统计离群点")
    print("   - 自适应下采样（根据点云大小选择体素大小）")
    print("   - 估计法线向量")
    
    print("2. 特征计算:")
    print("   - 计算FPFH（Fast Point Feature Histograms）特征")
    print("   - 为每个点生成33维特征描述符")
    
    print("3. 全局配准:")
    print("   - 使用RANSAC算法进行特征匹配")
    print("   - 寻找最佳的初始变换")
    print("   - 检查配准质量（fitness和RMSE）")
    
    print("4. ICP精配准:")
    print("   - 在全局配准基础上进行精细配准")
    print("   - 迭代优化变换矩阵")
    print("   - 最小化点到点距离")
    
    print("5. 合理性检查:")
    print("   - 平移距离 < 10米")
    print("   - 旋转角度 < 57度")
    print("   - 配准质量 > 0.1")
    
    print("6. 结果应用:")
    print("   - 将估计的自车变换应用到静止物体")
    print("   - 计算逆变换进行位置补偿")

def show_expected_logs():
    """显示预期的日志输出"""
    
    print(f"\n=== 预期日志输出示例 ===")
    print("""
=== Starting ego motion estimation from point clouds ===
Loaded point clouds - PCD1: 481522 points, PCD2: 482890 points
After outlier removal - PCD1: 465234 points, PCD2: 467123 points
Using voxel size: 0.5
After downsampling - PCD1: 8234 points, PCD2: 8456 points
Computing normals...
Computing FPFH features...
FPFH features computed - PCD1: 8234 features, PCD2: 8456 features
Performing global registration...
Global registration completed - Fitness: 0.7234, RMSE: 0.1456
Performing ICP refinement...
ICP refinement completed - Fitness: 0.8567, RMSE: 0.0987
Estimated ego motion:
  Translation: [0.8234, -0.1234, 0.0456] (norm: 0.8345m)
  Rotation: [0.0123, -0.0045, 0.0678] (norm: 0.0689rad)
Ego motion estimation successful
    """)

if __name__ == "__main__":
    test_auto_estimation()
    test_with_real_data()
    create_curl_command_auto()
    explain_estimation_process()
    show_expected_logs()
    
    print(f"\n=== 使用说明 ===")
    print("1. 启动修复版服务器:")
    print("   python app_fixed.py")
    print("2. 准备您的点云文件（.pcd格式）")
    print("3. 修改测试数据中的文件路径")
    print("4. 发送API请求（不包含egoPose参数）")
    print("5. 查看详细日志: tail -f fixed_object_tracking.log")
    
    print(f"\n=== 关键优势 ===")
    print("✅ 无需手动提供自车位姿")
    print("✅ 自动从点云数据估计运动")
    print("✅ 包含多层质量检查")
    print("✅ 自适应参数选择")
    print("✅ 详细的过程日志")
    
    print(f"\n=== 注意事项 ===")
    print("⚠️  需要足够的重叠区域进行配准")
    print("⚠️  点云质量影响估计精度")
    print("⚠️  大幅运动可能导致估计失败")
    print("⚠️  估计过程需要更多计算时间")
    
    print(f"\n=== 下一步 ===")
    print("请使用您的真实点云数据测试自动估计功能！")
