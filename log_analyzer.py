#!/usr/bin/env python3
"""
日志分析工具
"""

import re
import json
from datetime import datetime
from collections import defaultdict

def parse_log_file(log_file_path="object_tracking.log"):
    """解析日志文件"""
    
    try:
        with open(log_file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
    except FileNotFoundError:
        print(f"日志文件 {log_file_path} 不存在")
        return None
    
    # 日志行格式: [时间戳][REQ:请求ID][OBJ:物体ID] 消息
    log_pattern = r'\[([^\]]+)\](?:\[REQ:([^\]]+)\])?(?:\[OBJ:([^\]]+)\])?\s*(.*)'
    
    parsed_logs = []
    
    for line_num, line in enumerate(lines, 1):
        line = line.strip()
        if not line:
            continue
            
        match = re.match(log_pattern, line)
        if match:
            timestamp_str, request_id, object_id, message = match.groups()
            
            try:
                timestamp = datetime.strptime(timestamp_str, "%Y-%m-%d %H:%M:%S,%f")
            except ValueError:
                try:
                    timestamp = datetime.strptime(timestamp_str, "%Y-%m-%d %H:%M:%S")
                except ValueError:
                    timestamp = None
            
            parsed_logs.append({
                'line_num': line_num,
                'timestamp': timestamp,
                'request_id': request_id,
                'object_id': object_id,
                'message': message,
                'raw_line': line
            })
        else:
            # 处理多行日志（如矩阵数据）
            if parsed_logs:
                parsed_logs[-1]['message'] += '\n' + line
    
    return parsed_logs

def analyze_requests(parsed_logs):
    """分析请求统计"""
    
    if not parsed_logs:
        return {}
    
    requests = defaultdict(lambda: {
        'start_time': None,
        'end_time': None,
        'objects': set(),
        'stationary_objects': set(),
        'moving_objects': set(),
        'errors': [],
        'processing_times': {},
        'log_count': 0
    })
    
    for log in parsed_logs:
        req_id = log['request_id']
        if not req_id:
            continue
            
        req_data = requests[req_id]
        req_data['log_count'] += 1
        
        # 记录时间范围
        if log['timestamp']:
            if not req_data['start_time'] or log['timestamp'] < req_data['start_time']:
                req_data['start_time'] = log['timestamp']
            if not req_data['end_time'] or log['timestamp'] > req_data['end_time']:
                req_data['end_time'] = log['timestamp']
        
        # 记录物体
        if log['object_id']:
            req_data['objects'].add(log['object_id'])
        
        # 分析物体类型
        message = log['message']
        if '检测到静止物体' in message and log['object_id']:
            req_data['stationary_objects'].add(log['object_id'])
        elif '检测到运动物体' in message and log['object_id']:
            req_data['moving_objects'].add(log['object_id'])
        
        # 记录错误
        if 'ERROR' in message or '失败' in message:
            req_data['errors'].append({
                'object_id': log['object_id'],
                'message': message,
                'timestamp': log['timestamp']
            })
        
        # 提取处理时间
        if '耗时' in message:
            time_match = re.search(r'耗时[：:]\s*([\d.]+)', message)
            if time_match:
                processing_time = float(time_match.group(1))
                if '总耗时' in message:
                    req_data['processing_times']['total'] = processing_time
                elif '下载' in message:
                    req_data['processing_times']['download'] = processing_time
                elif log['object_id']:
                    req_data['processing_times'][log['object_id']] = processing_time
    
    return dict(requests)

def analyze_objects(parsed_logs):
    """分析物体处理统计"""
    
    objects = defaultdict(lambda: {
        'requests': set(),
        'type': 'unknown',
        'input_positions': [],
        'output_positions': [],
        'position_changes': [],
        'processing_times': [],
        'errors': []
    })
    
    for log in parsed_logs:
        obj_id = log['object_id']
        if not obj_id:
            continue
            
        obj_data = objects[obj_id]
        obj_data['requests'].add(log['request_id'])
        
        message = log['message']
        
        # 确定物体类型
        if '检测到静止物体' in message:
            obj_data['type'] = 'stationary'
        elif '检测到运动物体' in message:
            obj_data['type'] = 'moving'
        
        # 提取位置信息
        if '输入中心位置' in message or '原始中心' in message:
            pos_match = re.search(r'\[\s*([-\d.]+)\s+([-\d.]+)\s+([-\d.]+)\s*\]', message)
            if pos_match:
                position = [float(pos_match.group(i)) for i in range(1, 4)]
                obj_data['input_positions'].append(position)
        
        if '变换后中心' in message or '最终框位置' in message:
            pos_match = re.search(r'\[\s*([-\d.]+)\s+([-\d.]+)\s+([-\d.]+)\s*\]', message)
            if pos_match:
                position = [float(pos_match.group(i)) for i in range(1, 4)]
                obj_data['output_positions'].append(position)
        
        if '位置变化' in message and '距离' not in message:
            pos_match = re.search(r'\[\s*([-\d.]+)\s+([-\d.]+)\s+([-\d.]+)\s*\]', message)
            if pos_match:
                change = [float(pos_match.group(i)) for i in range(1, 4)]
                obj_data['position_changes'].append(change)
        
        # 提取处理时间
        if '处理完成，耗时' in message:
            time_match = re.search(r'耗时[：:]\s*([\d.]+)', message)
            if time_match:
                obj_data['processing_times'].append(float(time_match.group(1)))
        
        # 记录错误
        if 'ERROR' in message or '失败' in message:
            obj_data['errors'].append(message)
    
    return dict(objects)

def generate_report(log_file_path="object_tracking.log"):
    """生成分析报告"""
    
    print(f"=== 日志分析报告 ===")
    print(f"日志文件: {log_file_path}")
    print(f"分析时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    parsed_logs = parse_log_file(log_file_path)
    if not parsed_logs:
        return
    
    print(f"总日志行数: {len(parsed_logs)}")
    
    # 请求分析
    requests = analyze_requests(parsed_logs)
    print(f"\n=== 请求统计 ===")
    print(f"总请求数: {len(requests)}")
    
    for req_id, req_data in requests.items():
        print(f"\n请求 {req_id[:8]}:")
        if req_data['start_time'] and req_data['end_time']:
            duration = (req_data['end_time'] - req_data['start_time']).total_seconds()
            print(f"  时间范围: {req_data['start_time']} - {req_data['end_time']}")
            print(f"  总耗时: {duration:.3f}秒")
        
        print(f"  处理物体数: {len(req_data['objects'])}")
        print(f"  静止物体: {len(req_data['stationary_objects'])}")
        print(f"  运动物体: {len(req_data['moving_objects'])}")
        print(f"  错误数: {len(req_data['errors'])}")
        print(f"  日志行数: {req_data['log_count']}")
        
        if req_data['processing_times']:
            print(f"  处理时间: {req_data['processing_times']}")
    
    # 物体分析
    objects = analyze_objects(parsed_logs)
    print(f"\n=== 物体统计 ===")
    print(f"总物体数: {len(objects)}")
    
    for obj_id, obj_data in objects.items():
        print(f"\n物体 {obj_id}:")
        print(f"  类型: {obj_data['type']}")
        print(f"  处理次数: {len(obj_data['requests'])}")
        
        if obj_data['input_positions']:
            print(f"  输入位置: {obj_data['input_positions'][-1]}")
        if obj_data['output_positions']:
            print(f"  输出位置: {obj_data['output_positions'][-1]}")
        if obj_data['position_changes']:
            print(f"  位置变化: {obj_data['position_changes'][-1]}")
        
        if obj_data['processing_times']:
            avg_time = sum(obj_data['processing_times']) / len(obj_data['processing_times'])
            print(f"  平均处理时间: {avg_time:.3f}秒")
        
        if obj_data['errors']:
            print(f"  错误数: {len(obj_data['errors'])}")

if __name__ == "__main__":
    generate_report()
