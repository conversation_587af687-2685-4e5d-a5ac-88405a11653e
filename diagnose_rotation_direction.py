#!/usr/bin/env python3
"""
诊断旋转方向问题
"""

import numpy as np
from scipy.spatial.transform import Rotation as R

def analyze_rotation_direction():
    """分析旋转方向问题"""
    
    print("=== 旋转方向诊断 ===")
    
    # 模拟一个简单的向右转5度的情况
    print("1. 假设自车向右转5度（顺时针，从上往下看）")
    
    # 右转5度的旋转矩阵（绕Z轴负方向旋转）
    angle_deg = 5
    angle_rad = np.radians(angle_deg)
    
    print(f"旋转角度: {angle_deg}度 = {angle_rad:.4f}弧度")
    
    # 方法1：标准右转（绕Z轴负方向）
    rotation_right = R.from_euler('z', -angle_rad)  # 负号表示顺时针
    matrix_right = rotation_right.as_matrix()
    
    print(f"\n方法1 - 标准右转矩阵（绕Z轴负方向）:")
    for i, row in enumerate(matrix_right):
        print(f"  [{i}] {row}")
    
    # 方法2：左转（绕Z轴正方向）
    rotation_left = R.from_euler('z', angle_rad)   # 正号表示逆时针
    matrix_left = rotation_left.as_matrix()
    
    print(f"\n方法2 - 左转矩阵（绕Z轴正方向）:")
    for i, row in enumerate(matrix_left):
        print(f"  [{i}] {row}")
    
    # 测试点：前方的一个物体
    test_point = np.array([10.0, 0.0, 0.0])  # 前方10米
    print(f"\n测试点（前方10米）: {test_point}")
    
    # 应用右转变换
    rotated_right = matrix_right @ test_point
    print(f"右转后位置: {rotated_right}")
    print(f"  X变化: {rotated_right[0] - test_point[0]:.4f}")
    print(f"  Y变化: {rotated_right[1] - test_point[1]:.4f}")
    
    # 应用左转变换
    rotated_left = matrix_left @ test_point
    print(f"左转后位置: {rotated_left}")
    print(f"  X变化: {rotated_left[0] - test_point[0]:.4f}")
    print(f"  Y变化: {rotated_left[1] - test_point[1]:.4f}")
    
    print(f"\n预期行为（自车右转时）:")
    print(f"  - 前方物体应该向左偏移（Y坐标增加）")
    print(f"  - 这是因为自车转向右，物体相对位置向左")

def analyze_transform_direction():
    """分析变换方向问题"""
    
    print(f"\n=== 变换方向分析 ===")
    
    print("ICP配准的方向问题:")
    print("  - ICP通常计算从源点云到目标点云的变换")
    print("  - 即：T * PCD1 = PCD2")
    print("  - 但我们需要的是自车运动：从frame1到frame2")
    
    print(f"\n可能的问题:")
    print("1. 变换方向相反")
    print("   - ICP给出：PCD1 → PCD2")
    print("   - 我们需要：frame1 → frame2（可能是相反的）")
    
    print("2. 坐标系定义不同")
    print("   - 点云坐标系 vs 自车坐标系")
    print("   - 左手系 vs 右手系")
    
    print("3. 旋转约定不同")
    print("   - 顺时针 vs 逆时针")
    print("   - 欧拉角顺序（XYZ vs ZYX）")

def test_inverse_transform():
    """测试逆变换修正"""
    
    print(f"\n=== 测试逆变换修正 ===")
    
    # 模拟ICP得到的变换（假设这是错误方向的）
    icp_translation = np.array([0.8, -0.1, 0.0])
    icp_rotation_euler = np.array([0.0, 0.0, 0.05])  # 5度
    
    icp_rotation_matrix = R.from_euler('xyz', icp_rotation_euler).as_matrix()
    icp_transform = np.eye(4)
    icp_transform[:3, :3] = icp_rotation_matrix
    icp_transform[:3, 3] = icp_translation
    
    print(f"ICP得到的变换:")
    for i, row in enumerate(icp_transform):
        print(f"  [{i}] {row}")
    
    # 方法1：直接使用ICP结果
    print(f"\n方法1 - 直接使用ICP结果:")
    test_object = np.array([14.3054, 2.9938, 3.9605, 1.0])
    
    icp_inv = np.linalg.inv(icp_transform)
    result1 = icp_inv @ test_object
    print(f"  变换后: {result1[:3]}")
    print(f"  位置变化: {result1[:3] - test_object[:3]}")
    
    # 方法2：使用ICP结果的逆
    print(f"\n方法2 - 使用ICP结果的逆:")
    result2 = icp_transform @ test_object
    result2_inv = np.linalg.inv(icp_transform) @ result2
    print(f"  先正变换: {result2[:3]}")
    print(f"  再逆变换: {result2_inv[:3]}")
    
    # 方法3：反转旋转方向
    print(f"\n方法3 - 反转旋转方向:")
    corrected_rotation_euler = -icp_rotation_euler  # 反转旋转
    corrected_rotation_matrix = R.from_euler('xyz', corrected_rotation_euler).as_matrix()
    corrected_transform = np.eye(4)
    corrected_transform[:3, :3] = corrected_rotation_matrix
    corrected_transform[:3, 3] = icp_translation  # 保持平移
    
    corrected_inv = np.linalg.inv(corrected_transform)
    result3 = corrected_inv @ test_object
    print(f"  变换后: {result3[:3]}")
    print(f"  位置变化: {result3[:3] - test_object[:3]}")

def suggest_fixes():
    """建议修复方案"""
    
    print(f"\n=== 建议的修复方案 ===")
    
    print("1. 检查ICP变换方向:")
    print("   - 当前：T * PCD1 = PCD2")
    print("   - 可能需要：T * PCD2 = PCD1")
    print("   - 修复：使用 T.inverse() 作为自车变换")
    
    print("2. 反转旋转方向:")
    print("   - 当前：rotation_euler")
    print("   - 修复：-rotation_euler")
    
    print("3. 检查坐标系约定:")
    print("   - 确认X轴方向（前方）")
    print("   - 确认Y轴方向（左侧 vs 右侧）")
    print("   - 确认Z轴方向（上方）")
    
    print("4. 验证方法:")
    print("   - 使用已知的自车运动测试")
    print("   - 观察静止物体的位置变化方向")
    print("   - 确保符合物理直觉")

if __name__ == "__main__":
    analyze_rotation_direction()
    analyze_transform_direction()
    test_inverse_transform()
    suggest_fixes()
    
    print(f"\n=== 下一步行动 ===")
    print("1. 请告诉我具体的观测结果:")
    print("   - 自车实际是向哪个方向转的？")
    print("   - 算法预测的物体位置是向哪个方向偏移的？")
    print("   - 您期望的偏移方向是什么？")
    
    print("2. 我将根据您的反馈修正算法")
    print("3. 可能需要调整的地方:")
    print("   - ICP变换的方向")
    print("   - 旋转角度的符号")
    print("   - 坐标系的定义")
