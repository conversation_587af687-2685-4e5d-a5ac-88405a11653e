#!/usr/bin/env python3
"""
最终修复版本 - 彻底解决静止物体补偿问题
"""

from flask import Flask, request, jsonify
import numpy as np
from scipy.spatial.transform import Rotation as R
import open3d as o3d
import tempfile
import requests
import os
import time
import shutil
import traceback
import logging
from datetime import datetime
import uuid
import json

app = Flask(__name__)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('final_fix.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# 全局变量
current_request_id = None

def log_with_timestamp(message, level="INFO", obj_id=None):
    """带时间戳的日志输出"""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]
    prefix = f"[{timestamp}]"
    if current_request_id:
        prefix += f"[REQ:{current_request_id[:8]}]"
    if obj_id:
        prefix += f"[OBJ:{obj_id}]"
    
    full_message = f"{prefix} {message}"
    
    if level == "ERROR":
        logger.error(full_message)
    elif level == "WARNING":
        logger.warning(full_message)
    else:
        logger.info(full_message)
    
    print(full_message)

def log_matrix(matrix, name, obj_id=None):
    """记录矩阵数据"""
    log_with_timestamp(f"{name}:", obj_id=obj_id)
    for i, row in enumerate(matrix):
        row_str = " ".join([f"{val:8.4f}" for val in row])
        log_with_timestamp(f"  [{i}] [{row_str}]", obj_id=obj_id)

def log_vector(vector, name, obj_id=None):
    """记录向量数据"""
    vector_str = " ".join([f"{val:8.4f}" for val in vector])
    log_with_timestamp(f"{name}: [{vector_str}]", obj_id=obj_id)

def download_file_from_url(url, temp_dir):
    """下载文件"""
    log_with_timestamp(f"Starting download: {url}")
    
    headers = {'User-Agent': 'Mozilla/5.0 (compatible; FileDownloader/1.0)'}

    try:
        download_start = time.time()
        with requests.get(url, stream=True, timeout=30, headers=headers) as response:
            response.raise_for_status()
            
            suffix = ".pcd"
            temp_file = tempfile.NamedTemporaryFile(dir=temp_dir, suffix=suffix, delete=False)

            with open(temp_file.name, 'wb') as f:
                shutil.copyfileobj(response.raw, f)
            
            download_end = time.time()
            download_time = download_end - download_start
            file_size = os.path.getsize(temp_file.name) / 1024 / 1024  # MB
            
            log_with_timestamp(f"Download completed: {temp_file.name}")
            log_with_timestamp(f"Download time: {download_time:.3f}s, size: {file_size:.2f}MB")
            
            return temp_file.name

    except Exception as e:
        error_msg = f"Download failed {url}: {e}"
        log_with_timestamp(error_msg, "ERROR")
        raise RuntimeError(error_msg)

def estimate_ego_motion_robust(path1, path2):
    """鲁棒的自车运动估计 - 增强版"""
    try:
        log_with_timestamp("=== Starting ENHANCED ego motion estimation ===")

        # 加载点云
        pcd1 = o3d.io.read_point_cloud(path1)
        pcd2 = o3d.io.read_point_cloud(path2)

        log_with_timestamp(f"Loaded point clouds - PCD1: {len(pcd1.points)} points, PCD2: {len(pcd2.points)} points")

        if len(pcd1.points) == 0 or len(pcd2.points) == 0:
            log_with_timestamp("Empty point cloud detected, using identity matrix", "WARNING")
            return np.eye(4)

        # 移除离群点
        pcd1, _ = pcd1.remove_statistical_outlier(nb_neighbors=20, std_ratio=2.0)
        pcd2, _ = pcd2.remove_statistical_outlier(nb_neighbors=20, std_ratio=2.0)
        log_with_timestamp(f"After outlier removal - PCD1: {len(pcd1.points)} points, PCD2: {len(pcd2.points)} points")

        # 更精细的下采样
        voxel_size = 0.2  # 减小体素大小提高精度
        pcd1_down = pcd1.voxel_down_sample(voxel_size)
        pcd2_down = pcd2.voxel_down_sample(voxel_size)

        log_with_timestamp(f"After fine downsampling - PCD1: {len(pcd1_down.points)} points, PCD2: {len(pcd2_down.points)} points")

        # 估计法线
        pcd1_down.estimate_normals(o3d.geometry.KDTreeSearchParamHybrid(radius=voxel_size * 2, max_nn=30))
        pcd2_down.estimate_normals(o3d.geometry.KDTreeSearchParamHybrid(radius=voxel_size * 2, max_nn=30))

        # 计算FPFH特征用于全局配准
        radius_feature = voxel_size * 5
        fpfh1 = o3d.pipelines.registration.compute_fpfh_feature(
            pcd1_down, o3d.geometry.KDTreeSearchParamHybrid(radius=radius_feature, max_nn=100))
        fpfh2 = o3d.pipelines.registration.compute_fpfh_feature(
            pcd2_down, o3d.geometry.KDTreeSearchParamHybrid(radius=radius_feature, max_nn=100))

        log_with_timestamp(f"FPFH features computed - PCD1: {fpfh1.data.shape[1]} features, PCD2: {fpfh2.data.shape[1]} features")

        # 全局配准（RANSAC）
        distance_threshold = voxel_size * 1.5
        log_with_timestamp("Performing global registration (RANSAC)...")

        result_global = o3d.pipelines.registration.registration_ransac_based_on_feature_matching(
            pcd1_down, pcd2_down, fpfh1, fpfh2, True, distance_threshold,
            o3d.pipelines.registration.TransformationEstimationPointToPoint(False), 3,
            [o3d.pipelines.registration.CorrespondenceCheckerBasedOnEdgeLength(0.9),
             o3d.pipelines.registration.CorrespondenceCheckerBasedOnDistance(distance_threshold)],
            o3d.pipelines.registration.RANSACConvergenceCriteria(100000, 0.999))

        log_with_timestamp(f"Global registration - Fitness: {result_global.fitness:.4f}, RMSE: {result_global.inlier_rmse:.4f}")

        # ICP精配准
        log_with_timestamp("Performing ICP refinement...")
        result_icp = o3d.pipelines.registration.registration_icp(
            pcd1_down, pcd2_down, distance_threshold, result_global.transformation,
            o3d.pipelines.registration.TransformationEstimationPointToPoint())

        log_with_timestamp(f"ICP refinement - Fitness: {result_icp.fitness:.4f}, RMSE: {result_icp.inlier_rmse:.4f}")

        # 分析运动量
        icp_transform = result_icp.transformation
        translation = icp_transform[:3, 3]
        rotation_matrix = icp_transform[:3, :3]

        # 计算旋转角度
        rotation_matrix_copy = np.array(rotation_matrix, copy=True)
        rotation_euler = R.from_matrix(rotation_matrix_copy).as_euler('xyz')

        translation_norm = np.linalg.norm(translation)
        rotation_norm = np.linalg.norm(rotation_euler)

        log_with_timestamp(f"Detected motion:")
        log_with_timestamp(f"  Translation: [{translation[0]:.4f}, {translation[1]:.4f}, {translation[2]:.4f}] (norm: {translation_norm:.4f}m)")
        log_with_timestamp(f"  Rotation: [{rotation_euler[0]:.4f}, {rotation_euler[1]:.4f}, {rotation_euler[2]:.4f}] (norm: {rotation_norm:.4f}rad = {np.degrees(rotation_norm):.2f}deg)")

        # 质量检查
        if result_icp.fitness < 0.3:
            log_with_timestamp(f"WARNING: Low ICP fitness ({result_icp.fitness:.4f}), results may be unreliable", "WARNING")

        if translation_norm < 0.05 and rotation_norm < np.radians(1.0):
            log_with_timestamp(f"WARNING: Very small motion detected (T:{translation_norm:.3f}m, R:{np.degrees(rotation_norm):.1f}deg)", "WARNING")
            log_with_timestamp("This may indicate insufficient ego motion or poor point cloud quality", "WARNING")

        # 记录最终变换
        log_matrix(icp_transform, "ENHANCED ICP transformation (PCD1 -> PCD2)")

        return icp_transform

    except Exception as e:
        log_with_timestamp(f"Enhanced ego motion estimation failed: {e}", "ERROR")
        log_with_timestamp(f"Error details: {traceback.format_exc()}", "ERROR")
        return np.eye(4)

def process_static_object_final(path1, path2, bbox_center, bbox_size, bbox_rotation, ego_transform, obj_id):
    """最终版本的静止物体处理"""
    
    log_with_timestamp("=== FINAL VERSION: Processing static object ===", obj_id=obj_id)
    log_vector(bbox_center, "Input center", obj_id)
    
    start_time = time.time()
    
    try:
        # 如果没有自车变换，估计一个
        if ego_transform is None:
            log_with_timestamp("No ego transform provided, estimating from point clouds", obj_id=obj_id)
            icp_transform = estimate_ego_motion_robust(path1, path2)
        else:
            icp_transform = ego_transform
        
        log_matrix(icp_transform, "ICP transform (PCD1 -> PCD2)", obj_id)
        
        # 关键修复：测试三种不同的补偿方法
        original_center = np.array(bbox_center)
        original_center_homo = np.append(original_center, 1.0)
        
        # 方法A：直接使用ICP结果
        result_A_homo = np.dot(icp_transform, original_center_homo)
        result_A = result_A_homo[:3]
        change_A = result_A - original_center
        
        # 方法B：使用ICP的逆
        icp_inv = np.linalg.inv(icp_transform)
        result_B_homo = np.dot(icp_inv, original_center_homo)
        result_B = result_B_homo[:3]
        change_B = result_B - original_center
        
        # 方法C：不做任何变换（保持原位）
        result_C = original_center.copy()
        change_C = np.zeros(3)

        # 同时计算旋转补偿
        original_rotation = np.array(bbox_rotation)

        # 方法A：旋转补偿（直接使用ICP旋转）
        ego_rotation_A = icp_transform[:3, :3]
        original_rotation_matrix = R.from_euler('xyz', original_rotation).as_matrix()
        new_rotation_matrix_A = np.dot(ego_rotation_A, original_rotation_matrix)
        new_rotation_A = R.from_matrix(new_rotation_matrix_A).as_euler('xyz')
        rotation_change_A = new_rotation_A - original_rotation

        # 方法B：旋转补偿（使用ICP旋转的逆）
        ego_rotation_B = np.linalg.inv(icp_transform[:3, :3])
        new_rotation_matrix_B = np.dot(ego_rotation_B, original_rotation_matrix)
        new_rotation_B = R.from_matrix(new_rotation_matrix_B).as_euler('xyz')
        rotation_change_B = new_rotation_B - original_rotation

        # 方法C：不做旋转补偿
        new_rotation_C = original_rotation.copy()
        rotation_change_C = np.zeros(3)

        log_with_timestamp("=== Testing three compensation methods ===", obj_id=obj_id)
        
        log_with_timestamp("Method A (direct ICP):", obj_id=obj_id)
        log_vector(result_A, "  New position", obj_id)
        log_vector(change_A, "  Position change", obj_id)
        log_vector(new_rotation_A, "  New rotation", obj_id)
        log_vector(rotation_change_A, "  Rotation change", obj_id)
        log_with_timestamp(f"  Y change: {change_A[1]:.4f} ({'right' if change_A[1] > 0 else 'left'})", obj_id=obj_id)
        log_with_timestamp(f"  Total change: {np.linalg.norm(change_A):.4f}m", obj_id=obj_id)
        
        log_with_timestamp("Method B (ICP inverse):", obj_id=obj_id)
        log_vector(result_B, "  New position", obj_id)
        log_vector(change_B, "  Position change", obj_id)
        log_vector(new_rotation_B, "  New rotation", obj_id)
        log_vector(rotation_change_B, "  Rotation change", obj_id)
        log_with_timestamp(f"  Y change: {change_B[1]:.4f} ({'right' if change_B[1] > 0 else 'left'})", obj_id=obj_id)
        log_with_timestamp(f"  Total change: {np.linalg.norm(change_B):.4f}m", obj_id=obj_id)
        
        log_with_timestamp("Method C (no compensation):", obj_id=obj_id)
        log_vector(result_C, "  New position", obj_id)
        log_vector(change_C, "  Position change", obj_id)
        log_vector(new_rotation_C, "  New rotation", obj_id)
        log_vector(rotation_change_C, "  Rotation change", obj_id)
        log_with_timestamp(f"  Y change: {change_C[1]:.4f} ({'right' if change_C[1] > 0 else 'left'})", obj_id=obj_id)
        log_with_timestamp(f"  Total change: {np.linalg.norm(change_C):.4f}m", obj_id=obj_id)
        
        # 选择方法A：直接使用ICP结果
        chosen_result = result_A
        chosen_change = change_A
        chosen_rotation = new_rotation_A
        chosen_method = "A (direct ICP)"
        
        log_with_timestamp(f"*** CHOSEN METHOD: {chosen_method} ***", obj_id=obj_id)
        log_vector(chosen_result, "Final position", obj_id)
        log_vector(chosen_change, "Final change", obj_id)
        
        # 构建结果
        result = {
            "size3D": {
                "x": float(bbox_size[0]),
                "y": float(bbox_size[1]),
                "z": float(bbox_size[2]),
            },
            "center3D": {
                "x": float(chosen_result[0]),
                "y": float(chosen_result[1]),
                "z": float(chosen_result[2]),
            },
            "rotation3D": {
                "x": float(chosen_rotation[0]),
                "y": float(chosen_rotation[1]),
                "z": float(chosen_rotation[2]),
            },
            "points": 0,
            "compensation_test": {
                "method_A_direct": {
                    "center": [float(result_A[0]), float(result_A[1]), float(result_A[2])],
                    "change": [float(change_A[0]), float(change_A[1]), float(change_A[2])],
                    "y_direction": "right" if change_A[1] > 0 else "left",
                    "total_change": float(np.linalg.norm(change_A))
                },
                "method_B_inverse": {
                    "center": [float(result_B[0]), float(result_B[1]), float(result_B[2])],
                    "change": [float(change_B[0]), float(change_B[1]), float(change_B[2])],
                    "y_direction": "right" if change_B[1] > 0 else "left",
                    "total_change": float(np.linalg.norm(change_B))
                },
                "method_C_none": {
                    "center": [float(result_C[0]), float(result_C[1]), float(result_C[2])],
                    "change": [float(change_C[0]), float(change_C[1]), float(change_C[2])],
                    "y_direction": "none",
                    "total_change": float(np.linalg.norm(change_C))
                },
                "chosen_method": chosen_method
            }
        }
        
        processing_time = time.time() - start_time
        log_with_timestamp(f"Static object processing completed in {processing_time:.3f}s", obj_id=obj_id)
        
        return result
        
    except Exception as e:
        log_with_timestamp(f"Static object processing failed: {e}", "ERROR", obj_id)
        raise

@app.route('/predict', methods=['POST'])
def predict():
    global current_request_id
    current_request_id = str(uuid.uuid4())
    
    log_with_timestamp("=== New prediction request (FINAL FIX VERSION) ===")
    
    try:
        data = request.get_json()

        # Get parameters
        path1 = data.get('pcd1Url')
        path2 = data.get('pcd2Url')
        objects = data.get('objects', [])
        
        log_with_timestamp(f"Point cloud file 1: {path1}")
        log_with_timestamp(f"Point cloud file 2: {path2}")
        log_with_timestamp(f"Number of objects: {len(objects)}")
        
        # Create temporary directory and download files
        with tempfile.TemporaryDirectory() as temp_dir:
            download_start_time = time.time()
            
            path1 = download_file_from_url(path1, temp_dir)
            path2 = download_file_from_url(path2, temp_dir)
            
            download_end_time = time.time()
            download_duration = download_end_time - download_start_time
            
            # Process all objects
            results = []
            processing_start_time = time.time()
            
            for obj in objects:
                obj_id = obj.get("objectId", "unknown")
                size3D = obj.get("size3D", {})
                center3D = obj.get("center3D", {})
                rotation3D = obj.get("rotation3D", {})

                bbox_center = [center3D.get("x", 0), center3D.get("y", 0), center3D.get("z", 0)]
                bbox_size = [size3D.get("x", 1), size3D.get("y", 1), size3D.get("z", 1)]
                bbox_rotation = [rotation3D.get("x", 0), rotation3D.get("y", 0), rotation3D.get("z", 0)]
                
                try:
                    result = process_static_object_final(path1, path2, bbox_center, bbox_size, bbox_rotation, None, obj_id)
                    result["objectId"] = obj_id
                    results.append(result)
                    
                except Exception as e:
                    log_with_timestamp(f"Object {obj_id} processing failed: {e}", "ERROR", obj_id)
                    result = {
                        "objectId": obj_id,
                        "size3D": size3D,
                        "center3D": center3D,
                        "rotation3D": rotation3D,
                        "points": 0,
                        "error": str(e)
                    }
                    results.append(result)
            
            processing_end_time = time.time()
            processing_duration = processing_end_time - processing_start_time
            
            total_duration = processing_end_time - download_start_time
            log_with_timestamp(f"=== Prediction request completed in {total_duration:.3f}s ===")
            
            return jsonify({
                "status": "success",
                "result": results,
                "request_id": current_request_id,
                "processing_time": {
                    "download": download_duration,
                    "processing": processing_duration,
                    "total": total_duration
                },
                "version": "final_fix_with_compensation_test",
                "note": "Check compensation_test field to see all three methods",
                "timestamp": datetime.now().isoformat()
            })
            
    except Exception as e:
        error_msg = f"Error during processing: {str(e)}"
        log_with_timestamp(error_msg, "ERROR")
        
        return jsonify({
            "status": "fail",
            "result": error_msg,
            "request_id": current_request_id,
            "timestamp": datetime.now().isoformat()
        })

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5004, debug=True)
