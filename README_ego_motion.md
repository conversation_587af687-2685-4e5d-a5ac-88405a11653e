# 自车运动补偿功能说明

## 问题描述

之前的算法对于静止物体直接返回原始位置，没有考虑自车的运动。这导致静止物体在新的一帧中的位置和老的一帧相对自车的位置完全一样，没有随着自车的移动而变化。

## 解决方案

### 1. 核心改进

- **添加自车运动补偿**：对于检测到的静止物体，应用自车运动的逆变换
- **支持自车位姿输入**：API现在可以接收自车位姿信息
- **全局变换估计**：如果没有自车位姿信息，通过点云配准估计全局变换

### 2. API 使用方法

#### 新增参数

在原有的API基础上，新增了可选的 `egoPose` 参数：

```json
{
  "pcd1Url": "path/to/frame1.pcd",
  "pcd2Url": "path/to/frame2.pcd",
  "egoPose": {
    "translation": [1.0, 0.0, 0.0],  // 自车平移 [x, y, z]
    "rotation": [0.0, 0.0, 0.087]     // 自车旋转 [rx, ry, rz] 弧度
  },
  "objects": [...]
}
```

#### 支持的自车位姿格式

1. **平移+旋转格式**：
```json
"egoPose": {
  "translation": [x, y, z],
  "rotation": [rx, ry, rz]
}
```

2. **变换矩阵格式**：
```json
"egoPose": {
  "transform_matrix": [
    [r11, r12, r13, tx],
    [r21, r22, r23, ty],
    [r31, r32, r33, tz],
    [0,   0,   0,   1]
  ]
}
```

3. **两个位姿格式**：
```json
"egoPose": {
  "pose1": {"x": x1, "y": y1, "z": z1, "rx": rx1, "ry": ry1, "rz": rz1},
  "pose2": {"x": x2, "y": y2, "z": z2, "rx": rx2, "ry": ry2, "rz": rz2}
}
```

### 3. 算法逻辑

#### 对于静止物体：
1. **检测静止物体**：通过比较两帧中物体点云的重心距离
2. **应用自车运动补偿**：
   - 如果提供了自车位姿，直接使用
   - 如果没有提供，通过全局点云配准估计自车运动
3. **计算新位置**：对静止物体应用自车运动的逆变换

#### 对于运动物体：
- 继续使用原有的ICP配准算法

### 4. 示例

假设自车向前移动1米，向右转5度：

```python
# 输入
ego_pose = {
    "translation": [1.0, 0.0, 0.0],  # 向前1米
    "rotation": [0.0, 0.0, 0.087]    # 向右转5度
}

object_frame1 = {"x": 5.0, "y": 2.0, "z": 0.0}  # 前方5米，右侧2米

# 输出（静止物体的新位置）
object_frame2 = {"x": 4.16, "y": 1.64, "z": 0.0}  # 相对位置发生变化
```

### 5. 向后兼容

- 如果不提供 `egoPose` 参数，算法会尝试通过全局点云配准估计自车运动
- 原有的API调用方式仍然有效

### 6. 性能优化

- 静止物体检测避免了不必要的ICP计算
- 全局变换估计使用下采样提高速度
- 自适应阈值根据物体大小调整

## 测试

运行测试脚本验证功能：

```bash
python test_ego_motion.py
```

这将验证自车运动补偿的数学逻辑是否正确。
