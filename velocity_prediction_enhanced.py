#!/usr/bin/env python3
"""
增强版速度预测功能
"""

import numpy as np
from scipy.spatial.transform import Rotation as R
import open3d as o3d
from datetime import datetime
import json

class VelocityPredictor:
    """速度预测器"""
    
    def __init__(self):
        self.object_history = {}  # 存储物体历史轨迹
        self.max_history = 10     # 最大历史帧数
        
    def predict_velocity_from_pointclouds(self, pcd1_path, pcd2_path, bbox_center, bbox_size, obj_id, time_delta=0.1):
        """从点云中预测物体速度"""
        
        print(f"=== 预测物体 {obj_id} 的速度 ===")
        
        try:
            # 方法1：基于物体在两帧中的位置差异
            velocity_from_position = self._estimate_velocity_from_position_change(
                pcd1_path, pcd2_path, bbox_center, bbox_size, obj_id, time_delta
            )
            
            # 方法2：基于历史轨迹
            velocity_from_history = self._estimate_velocity_from_history(obj_id, bbox_center, time_delta)
            
            # 方法3：基于点云运动分析
            velocity_from_motion = self._estimate_velocity_from_motion_analysis(
                pcd1_path, pcd2_path, bbox_center, bbox_size, obj_id
            )
            
            # 融合多种方法
            final_velocity = self._fuse_velocity_estimates(
                velocity_from_position, velocity_from_history, velocity_from_motion
            )
            
            # 更新历史记录
            self._update_object_history(obj_id, bbox_center, final_velocity)
            
            print(f"最终预测速度: {final_velocity}")
            return final_velocity
            
        except Exception as e:
            print(f"速度预测失败: {e}")
            return np.array([0.0, 0.0, 0.0])
    
    def _estimate_velocity_from_position_change(self, pcd1_path, pcd2_path, bbox_center, bbox_size, obj_id, time_delta):
        """方法1：基于位置变化估计速度"""
        
        print(f"方法1：基于位置变化估计速度")
        
        try:
            # 加载点云
            pcd1 = o3d.io.read_point_cloud(pcd1_path)
            pcd2 = o3d.io.read_point_cloud(pcd2_path)
            
            # 在两个点云中寻找对应的物体
            center1 = self._find_object_in_pointcloud(pcd1, bbox_center, bbox_size)
            center2 = self._find_object_in_pointcloud(pcd2, bbox_center, bbox_size)
            
            if center1 is not None and center2 is not None:
                # 计算位置变化
                position_change = center2 - center1
                velocity = position_change / time_delta
                
                print(f"  PCD1中位置: {center1}")
                print(f"  PCD2中位置: {center2}")
                print(f"  位置变化: {position_change}")
                print(f"  估计速度: {velocity}")
                
                return velocity
            else:
                print(f"  无法在点云中找到物体")
                return np.array([0.0, 0.0, 0.0])
                
        except Exception as e:
            print(f"  位置变化估计失败: {e}")
            return np.array([0.0, 0.0, 0.0])
    
    def _find_object_in_pointcloud(self, pcd, bbox_center, bbox_size):
        """在点云中寻找物体的中心"""
        
        try:
            # 定义搜索区域（扩大边界框）
            center = np.array(bbox_center)
            size = np.array(bbox_size)
            
            # 扩大搜索范围
            search_min = center - size * 0.6
            search_max = center + size * 0.6
            
            # 提取搜索区域内的点
            points = np.asarray(pcd.points)
            
            # 筛选在边界框内的点
            mask = np.all((points >= search_min) & (points <= search_max), axis=1)
            object_points = points[mask]
            
            if len(object_points) > 10:  # 至少需要10个点
                # 计算点云中心
                object_center = np.mean(object_points, axis=0)
                return object_center
            else:
                return None
                
        except Exception as e:
            print(f"    点云搜索失败: {e}")
            return None
    
    def _estimate_velocity_from_history(self, obj_id, current_center, time_delta):
        """方法2：基于历史轨迹估计速度"""
        
        print(f"方法2：基于历史轨迹估计速度")
        
        if obj_id not in self.object_history:
            print(f"  无历史数据")
            return np.array([0.0, 0.0, 0.0])
        
        history = self.object_history[obj_id]
        
        if len(history['positions']) < 2:
            print(f"  历史数据不足")
            return np.array([0.0, 0.0, 0.0])
        
        # 使用最近几帧计算平均速度
        recent_positions = history['positions'][-3:]  # 最近3帧
        recent_times = history['timestamps'][-3:]
        
        if len(recent_positions) >= 2:
            # 计算平均速度
            total_displacement = np.array(recent_positions[-1]) - np.array(recent_positions[0])
            total_time = recent_times[-1] - recent_times[0]
            
            if total_time > 0:
                avg_velocity = total_displacement / total_time
                print(f"  历史平均速度: {avg_velocity}")
                return avg_velocity
        
        print(f"  历史数据无效")
        return np.array([0.0, 0.0, 0.0])
    
    def _estimate_velocity_from_motion_analysis(self, pcd1_path, pcd2_path, bbox_center, bbox_size, obj_id):
        """方法3：基于点云运动分析估计速度"""
        
        print(f"方法3：基于点云运动分析估计速度")
        
        try:
            # 这里可以实现更复杂的点云运动分析
            # 比如：光流分析、特征点跟踪等
            
            # 简化实现：基于点云密度变化
            pcd1 = o3d.io.read_point_cloud(pcd1_path)
            pcd2 = o3d.io.read_point_cloud(pcd2_path)
            
            # 分析物体区域的点云变化
            motion_vector = self._analyze_pointcloud_motion(pcd1, pcd2, bbox_center, bbox_size)
            
            print(f"  运动分析结果: {motion_vector}")
            return motion_vector
            
        except Exception as e:
            print(f"  运动分析失败: {e}")
            return np.array([0.0, 0.0, 0.0])
    
    def _analyze_pointcloud_motion(self, pcd1, pcd2, bbox_center, bbox_size):
        """分析点云运动"""
        
        try:
            # 简化的运动分析
            # 实际应用中可以使用更复杂的算法
            
            center = np.array(bbox_center)
            size = np.array(bbox_size)
            
            # 在两个点云中提取物体区域
            points1 = self._extract_object_points(pcd1, center, size)
            points2 = self._extract_object_points(pcd2, center, size)
            
            if len(points1) > 10 and len(points2) > 10:
                # 计算质心变化
                centroid1 = np.mean(points1, axis=0)
                centroid2 = np.mean(points2, axis=0)
                
                motion = centroid2 - centroid1
                return motion / 0.1  # 假设时间间隔0.1秒
            
            return np.array([0.0, 0.0, 0.0])
            
        except Exception as e:
            print(f"    运动分析异常: {e}")
            return np.array([0.0, 0.0, 0.0])
    
    def _extract_object_points(self, pcd, center, size):
        """提取物体区域的点"""
        
        points = np.asarray(pcd.points)
        
        # 定义边界框
        min_bound = center - size * 0.5
        max_bound = center + size * 0.5
        
        # 筛选点
        mask = np.all((points >= min_bound) & (points <= max_bound), axis=1)
        return points[mask]
    
    def _fuse_velocity_estimates(self, vel_position, vel_history, vel_motion):
        """融合多种速度估计"""
        
        print(f"融合速度估计:")
        print(f"  位置变化法: {vel_position}")
        print(f"  历史轨迹法: {vel_history}")
        print(f"  运动分析法: {vel_motion}")
        
        # 简单的加权融合
        weights = [0.5, 0.3, 0.2]  # 位置变化权重最高
        
        velocities = [vel_position, vel_history, vel_motion]
        
        # 计算加权平均
        fused_velocity = np.zeros(3)
        total_weight = 0
        
        for i, (vel, weight) in enumerate(zip(velocities, weights)):
            if np.linalg.norm(vel) > 0.01:  # 忽略接近零的速度
                fused_velocity += vel * weight
                total_weight += weight
        
        if total_weight > 0:
            fused_velocity /= total_weight
        
        print(f"  融合结果: {fused_velocity}")
        return fused_velocity
    
    def _update_object_history(self, obj_id, position, velocity):
        """更新物体历史记录"""
        
        current_time = datetime.now().timestamp()
        
        if obj_id not in self.object_history:
            self.object_history[obj_id] = {
                'positions': [],
                'velocities': [],
                'timestamps': []
            }
        
        history = self.object_history[obj_id]
        
        # 添加新记录
        history['positions'].append(position)
        history['velocities'].append(velocity)
        history['timestamps'].append(current_time)
        
        # 限制历史长度
        if len(history['positions']) > self.max_history:
            history['positions'] = history['positions'][-self.max_history:]
            history['velocities'] = history['velocities'][-self.max_history:]
            history['timestamps'] = history['timestamps'][-self.max_history:]

def test_velocity_prediction():
    """测试速度预测功能"""
    
    print("=== 测试速度预测功能 ===")
    
    predictor = VelocityPredictor()
    
    # 模拟物体数据
    test_objects = [
        {
            "objectId": "car_1",
            "center3D": [20.0, 5.0, 3.5],
            "size3D": [4.5, 2.0, 1.8],
            "expected_velocity": [10.0, 2.0, 0.0]  # 期望的速度
        },
        {
            "objectId": "pedestrian_1", 
            "center3D": [15.0, 2.0, 1.0],
            "size3D": [0.6, 0.6, 1.8],
            "expected_velocity": [1.5, 0.5, 0.0]
        }
    ]
    
    for obj in test_objects:
        print(f"\n测试物体: {obj['objectId']}")
        
        predicted_velocity = predictor.predict_velocity_from_pointclouds(
            "dummy_pcd1.pcd",  # 实际使用时替换为真实路径
            "dummy_pcd2.pcd",
            obj["center3D"],
            obj["size3D"],
            obj["objectId"]
        )
        
        expected = np.array(obj["expected_velocity"])
        error = np.linalg.norm(predicted_velocity - expected)
        
        print(f"预测速度: {predicted_velocity}")
        print(f"期望速度: {expected}")
        print(f"预测误差: {error:.3f} m/s")

if __name__ == "__main__":
    test_velocity_prediction()
    
    print(f"\n=== 速度预测方法总结 ===")
    print("1. 位置变化法：基于物体在两帧中的位置差异")
    print("2. 历史轨迹法：基于物体的历史运动轨迹")
    print("3. 运动分析法：基于点云的运动分析")
    print("4. 融合方法：加权组合多种估计结果")
    
    print(f"\n=== 集成到主系统 ===")
    print("可以将VelocityPredictor集成到app_final_fix.py中")
    print("当velocity为None时，自动调用速度预测功能")
