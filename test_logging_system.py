#!/usr/bin/env python3
"""
测试日志系统
"""

import requests
import json
import time

def test_api_with_detailed_logging():
    """测试带详细日志的API调用"""
    
    # 测试数据
    test_data = {
        "pcd1Url": "https://example.com/frame1.pcd",  # 这会失败，但可以看到日志
        "pcd2Url": "https://example.com/frame2.pcd",
        "egoPose": {
            "translation": [1.0, 0.0, 0.0],  # 向前移动1米
            "rotation": [0.0, 0.0, 0.087]     # 向右转5度
        },
        "objects": [
            {
                "objectId": "test_static_obj",
                "center3D": {"x": 5.0, "y": 2.0, "z": 0.0},
                "size3D": {"x": 2.0, "y": 1.0, "z": 1.5},
                "rotation3D": {"x": 0.0, "y": 0.0, "z": 0.0}
            },
            {
                "objectId": "test_moving_obj", 
                "center3D": {"x": -3.0, "y": -1.0, "z": 0.5},
                "size3D": {"x": 1.5, "y": 1.5, "z": 2.0},
                "rotation3D": {"x": 0.0, "y": 0.0, "z": 0.5}
            }
        ]
    }
    
    print("=== 测试API请求 ===")
    print(json.dumps(test_data, indent=2))
    
    # 如果服务器在运行，可以取消注释进行实际测试
    """
    try:
        print("\n发送API请求...")
        response = requests.post(
            "http://localhost:5000/predict",
            json=test_data,
            timeout=60
        )
        
        print(f"响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("API响应:")
            print(json.dumps(result, indent=2))
        else:
            print(f"API错误: {response.text}")
            
    except requests.exceptions.RequestException as e:
        print(f"请求失败: {e}")
    """

def show_expected_log_format():
    """展示预期的日志格式"""
    
    print("\n=== 预期的详细日志格式 ===")
    print("""
[2024-01-15 14:30:15.123][REQ:a1b2c3d4][OBJ:test_static_obj] 开始处理物体跟踪
[2024-01-15 14:30:15.124][REQ:a1b2c3d4][OBJ:test_static_obj] 点云文件1: /tmp/frame1.pcd
[2024-01-15 14:30:15.125][REQ:a1b2c3d4][OBJ:test_static_obj] 点云文件2: /tmp/frame2.pcd
[2024-01-15 14:30:15.126][REQ:a1b2c3d4][OBJ:test_static_obj] 输入中心位置: [  5.0000   2.0000   0.0000]
[2024-01-15 14:30:15.127][REQ:a1b2c3d4][OBJ:test_static_obj] 输入框大小: [  2.0000   1.0000   1.5000]
[2024-01-15 14:30:15.128][REQ:a1b2c3d4][OBJ:test_static_obj] 输入旋转角度: [  0.0000   0.0000   0.0000]
[2024-01-15 14:30:15.129][REQ:a1b2c3d4][OBJ:test_static_obj] 点云加载成功 - PCD1: 15234点, PCD2: 15187点
[2024-01-15 14:30:15.130][REQ:a1b2c3d4][OBJ:test_static_obj] 裁剪区域大小: [  5.0000   2.5000   3.7500]
[2024-01-15 14:30:15.135][REQ:a1b2c3d4][OBJ:test_static_obj] 点云裁剪完成 - PCD1裁剪后: 156点, PCD2裁剪后: 148点
[2024-01-15 14:30:15.136][REQ:a1b2c3d4][OBJ:test_static_obj] 提供的自车变换矩阵:
[2024-01-15 14:30:15.137][REQ:a1b2c3d4][OBJ:test_static_obj]   [0] [  0.9962  -0.0872   0.0000   1.0000]
[2024-01-15 14:30:15.138][REQ:a1b2c3d4][OBJ:test_static_obj]   [1] [  0.0872   0.9962   0.0000   0.0000]
[2024-01-15 14:30:15.139][REQ:a1b2c3d4][OBJ:test_static_obj]   [2] [  0.0000   0.0000   1.0000   0.0000]
[2024-01-15 14:30:15.140][REQ:a1b2c3d4][OBJ:test_static_obj]   [3] [  0.0000   0.0000   0.0000   1.0000]
[2024-01-15 14:30:15.141][REQ:a1b2c3d4][OBJ:test_static_obj] 开始静止物体检测
[2024-01-15 14:30:15.142][REQ:a1b2c3d4][OBJ:test_static_obj] 静止检测 - 点云1点数: 156, 点云2点数: 148
[2024-01-15 14:30:15.145][REQ:a1b2c3d4][OBJ:test_static_obj] 点云1重心: [  5.1230   2.0450   0.0120]
[2024-01-15 14:30:15.146][REQ:a1b2c3d4][OBJ:test_static_obj] 点云2重心: [  5.1340   2.0510   0.0150]
[2024-01-15 14:30:15.147][REQ:a1b2c3d4][OBJ:test_static_obj] 重心距离: 0.0250m, 阈值: 0.1000m
[2024-01-15 14:30:15.148][REQ:a1b2c3d4][OBJ:test_static_obj] 静止判断结果: True
[2024-01-15 14:30:15.149][REQ:a1b2c3d4][OBJ:test_static_obj] 静止物体检测结果: True
[2024-01-15 14:30:15.150][REQ:a1b2c3d4][OBJ:test_static_obj] *** 检测到静止物体，应用自车运动补偿 ***
[2024-01-15 14:30:15.155][REQ:a1b2c3d4][OBJ:test_static_obj] 自车逆变换矩阵:
[2024-01-15 14:30:15.156][REQ:a1b2c3d4][OBJ:test_static_obj]   [0] [  0.9962   0.0872   0.0000  -0.9962]
[2024-01-15 14:30:15.157][REQ:a1b2c3d4][OBJ:test_static_obj]   [1] [ -0.0872   0.9962   0.0000  -0.0872]
[2024-01-15 14:30:15.158][REQ:a1b2c3d4][OBJ:test_static_obj]   [2] [  0.0000   0.0000   1.0000   0.0000]
[2024-01-15 14:30:15.159][REQ:a1b2c3d4][OBJ:test_static_obj]   [3] [  0.0000   0.0000   0.0000   1.0000]
[2024-01-15 14:30:15.160][REQ:a1b2c3d4][OBJ:test_static_obj] 原始中心: [  5.0000   2.0000   0.0000]
[2024-01-15 14:30:15.161][REQ:a1b2c3d4][OBJ:test_static_obj] 变换后中心: [  4.1591   1.6438   0.0000]
[2024-01-15 14:30:15.162][REQ:a1b2c3d4][OBJ:test_static_obj] 位置变化: [ -0.8409  -0.3562   0.0000]
[2024-01-15 14:30:15.163][REQ:a1b2c3d4][OBJ:test_static_obj] 位置变化距离: 0.9140m
[2024-01-15 14:30:15.164][REQ:a1b2c3d4][OBJ:test_static_obj] 原始旋转: [  0.0000   0.0000   0.0000]
[2024-01-15 14:30:15.165][REQ:a1b2c3d4][OBJ:test_static_obj] 变换后旋转: [  0.0000   0.0000  -0.0870]
[2024-01-15 14:30:15.166][REQ:a1b2c3d4][OBJ:test_static_obj] 旋转变化: [  0.0000   0.0000  -0.0870]
[2024-01-15 14:30:15.167][REQ:a1b2c3d4][OBJ:test_static_obj] 静止物体处理完成，耗时: 0.045秒
[2024-01-15 14:30:15.168][REQ:a1b2c3d4][OBJ:test_static_obj] === 静止物体输出结果 ===
[2024-01-15 14:30:15.169][REQ:a1b2c3d4][OBJ:test_static_obj] Center: [  4.1591   1.6438   0.0000]
[2024-01-15 14:30:15.170][REQ:a1b2c3d4][OBJ:test_static_obj] Size: [  2.0000   1.0000   1.5000]
[2024-01-15 14:30:15.171][REQ:a1b2c3d4][OBJ:test_static_obj] Rotation: [  0.0000   0.0000  -0.0870]
[2024-01-15 14:30:15.172][REQ:a1b2c3d4][OBJ:test_static_obj] Points: 156
    """)

def show_log_file_info():
    """显示日志文件信息"""
    
    print("\n=== 日志文件信息 ===")
    print("""
日志会同时输出到：
1. Console (标准输出)
2. 文件: object_tracking.log

日志级别：
- INFO: 正常处理信息
- WARNING: 警告信息（如阈值修正）
- ERROR: 错误信息

日志格式：
[时间戳][请求ID][物体ID] 消息内容

特殊标记：
- *** 重要信息 ***
- === 阶段分隔 ===
- 矩阵数据会格式化显示
- 向量数据会对齐显示
    """)

if __name__ == "__main__":
    test_api_with_detailed_logging()
    show_expected_log_format()
    show_log_file_info()
