#!/usr/bin/env python3
"""
测试日志输出功能
"""

import requests
import json

def test_api_with_logging():
    """测试API调用并查看日志输出"""
    
    # 模拟API请求数据
    test_data = {
        "pcd1Url": "http://example.com/frame1.pcd",  # 这会失败，但可以看到日志
        "pcd2Url": "http://example.com/frame2.pcd",
        "egoPose": {
            "translation": [1.0, 0.0, 0.0],  # 向前移动1米
            "rotation": [0.0, 0.0, 0.087]     # 向右转5度
        },
        "objects": [
            {
                "objectId": "test_obj_001",
                "center3D": {"x": 5.0, "y": 2.0, "z": 0.0},
                "size3D": {"x": 2.0, "y": 1.0, "z": 1.5},
                "rotation3D": {"x": 0.0, "y": 0.0, "z": 0.0}
            },
            {
                "objectId": "test_obj_002", 
                "center3D": {"x": -3.0, "y": -1.0, "z": 0.5},
                "size3D": {"x": 1.5, "y": 1.5, "z": 2.0},
                "rotation3D": {"x": 0.0, "y": 0.0, "z": 0.5}
            }
        ]
    }
    
    print("=== 测试API请求数据 ===")
    print(json.dumps(test_data, indent=2))
    
    # 如果服务器在运行，可以取消注释下面的代码进行实际测试
    """
    try:
        response = requests.post(
            "http://localhost:5000/predict",
            json=test_data,
            timeout=30
        )
        
        print(f"\n=== API响应状态: {response.status_code} ===")
        print(response.json())
        
    except requests.exceptions.RequestException as e:
        print(f"API请求失败: {e}")
    """

def test_console_output_format():
    """展示预期的console输出格式"""
    
    print("\n=== 预期的Console输出格式示例 ===")
    print("""
=== 预测请求开始 ===
点云文件1: /tmp/frame1.pcd
点云文件2: /tmp/frame2.pcd
物体数量: 2
自车位姿信息: 已提供
自车变换矩阵:
[[ 0.9961947  -0.08715574  0.          1.        ]
 [ 0.08715574  0.9961947   0.          0.        ]
 [ 0.          0.          1.          0.        ]
 [ 0.          0.          0.          1.        ]]
处理的物体列表:
  1. objectId: test_obj_001, center: [5.000, 2.000, 0.000]
  2. objectId: test_obj_002, center: [-3.000, -1.000, 0.500]

=== objectId=test_obj_001 处理开始 ===
原始框位置 - center: [5.000, 2.000, 0.000]
原始框大小 - size: [2.000, 1.000, 1.500]
原始框旋转 - rotation: [0.000, 0.000, 0.000]
静止物体检测结果: True
点云1点数: 150, 点云2点数: 145
*** 检测到静止物体，应用自车运动补偿 ***
使用提供的自车变换矩阵:
自车变换矩阵:
[[ 0.9961947  -0.08715574  0.          1.        ]
 [ 0.08715574  0.9961947   0.          0.        ]
 [ 0.          0.          1.          0.        ]
 [ 0.          0.          0.          1.        ]]
原始中心: [5.000, 2.000, 0.000]
变换后中心: [4.159, 1.644, 0.000]
原始旋转: [0.000, 0.000, 0.000]
变换后旋转: [0.000, 0.000, -0.087]
objectId=test_obj_001 run_icp耗时: 0.05 秒
预测框位置 - center: [4.159, 1.644, 0.000]
预测框旋转 - rotation: [0.000, 0.000, -0.087]
位置变化 - delta: [-0.841, -0.356, 0.000]
位置变化距离: 0.914m
*** 检测为静止物体，已应用自车运动补偿 ***
最终框位置 - center: [4.159, 1.644, 0.000]
最终框旋转 - rotation: [0.000, 0.000, -0.087]
最终位置变化: [-0.841, -0.356, 0.000]
=== objectId=test_obj_001 处理完成 ===
    """)

if __name__ == "__main__":
    test_api_with_logging()
    test_console_output_format()
