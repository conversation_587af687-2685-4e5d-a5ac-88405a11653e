#!/usr/bin/env python3
"""
测试静止物体专用版本
"""

import requests
import json
import numpy as np
from scipy.spatial.transform import Rotation as R

def test_static_object_with_ego_motion():
    """测试带自车运动的静止物体"""
    
    print("=== 测试静止物体自车运动补偿 ===")
    
    # 模拟自车运动：向前1米，向右转5度
    ego_translation = [1.0, 0.0, 0.0]
    ego_rotation = [0.0, 0.0, np.radians(5)]  # 5度转弧度
    
    test_data = {
        "pcd1Url": "https://example.com/frame1.pcd",  # 这会失败，但可以看到逻辑
        "pcd2Url": "https://example.com/frame2.pcd",
        "egoPose": {
            "translation": ego_translation,
            "rotation": ego_rotation
        },
        "objects": [
            {
                "objectId": "static_test_obj",
                "center3D": {"x": 5.0, "y": 2.0, "z": 0.0},
                "size3D": {"x": 2.0, "y": 1.0, "z": 1.5},
                "rotation3D": {"x": 0.0, "y": 0.0, "z": 0.0}
            }
        ]
    }
    
    print("测试数据:")
    print(json.dumps(test_data, indent=2))
    
    # 计算预期结果
    print(f"\n=== 预期结果计算 ===")
    
    # 构建自车变换矩阵
    ego_rotation_matrix = R.from_euler('xyz', ego_rotation).as_matrix()
    ego_transform = np.eye(4)
    ego_transform[:3, :3] = ego_rotation_matrix
    ego_transform[:3, 3] = ego_translation
    
    print(f"自车变换矩阵:")
    for i, row in enumerate(ego_transform):
        print(f"  [{i}] {row}")
    
    # 计算逆变换
    ego_transform_inv = np.linalg.inv(ego_transform)
    print(f"自车逆变换矩阵:")
    for i, row in enumerate(ego_transform_inv):
        print(f"  [{i}] {row}")
    
    # 应用到静止物体
    original_center = np.array([5.0, 2.0, 0.0])
    original_center_homo = np.append(original_center, 1.0)
    
    new_center_homo = np.dot(ego_transform_inv, original_center_homo)
    new_center = new_center_homo[:3]
    
    position_change = new_center - original_center
    
    print(f"原始位置: {original_center}")
    print(f"预期新位置: {new_center}")
    print(f"预期位置变化: {position_change}")
    print(f"预期位置变化距离: {np.linalg.norm(position_change):.4f}m")
    
    # 如果服务器在运行，可以取消注释进行实际测试
    """
    try:
        print(f"\n=== 发送API请求 ===")
        response = requests.post(
            "http://localhost:5001/predict",
            json=test_data,
            timeout=60
        )
        
        print(f"响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("API响应:")
            print(json.dumps(result, indent=2))
            
            # 分析结果
            if result.get("status") == "success" and result.get("result"):
                obj_result = result["result"][0]
                actual_center = obj_result.get("center3D", {})
                actual_position = np.array([actual_center.get("x", 0), actual_center.get("y", 0), actual_center.get("z", 0)])
                actual_change = actual_position - original_center
                
                print(f"\n=== 结果对比 ===")
                print(f"预期位置: {new_center}")
                print(f"实际位置: {actual_position}")
                print(f"预期变化: {position_change}")
                print(f"实际变化: {actual_change}")
                print(f"误差: {np.linalg.norm(actual_change - position_change):.6f}m")
                
        else:
            print(f"API错误: {response.text}")
            
    except requests.exceptions.RequestException as e:
        print(f"请求失败: {e}")
    """

def test_no_ego_motion():
    """测试无自车运动的情况"""
    
    print(f"\n=== 测试无自车运动 ===")
    
    test_data = {
        "pcd1Url": "https://example.com/frame1.pcd",
        "pcd2Url": "https://example.com/frame2.pcd",
        # 不提供egoPose，应该假设无运动
        "objects": [
            {
                "objectId": "static_no_ego_motion",
                "center3D": {"x": 3.0, "y": -1.0, "z": 0.5},
                "size3D": {"x": 1.5, "y": 1.5, "z": 2.0},
                "rotation3D": {"x": 0.0, "y": 0.0, "z": 0.5}
            }
        ]
    }
    
    print("测试数据:")
    print(json.dumps(test_data, indent=2))
    print("预期结果: 位置应该保持不变（因为无自车运动）")

def test_multiple_objects():
    """测试多个物体"""
    
    print(f"\n=== 测试多个物体 ===")
    
    test_data = {
        "pcd1Url": "https://example.com/frame1.pcd",
        "pcd2Url": "https://example.com/frame2.pcd",
        "egoPose": {
            "translation": [0.5, 0.0, 0.0],  # 向前0.5米
            "rotation": [0.0, 0.0, 0.0]      # 无旋转
        },
        "objects": [
            {
                "objectId": "obj_1",
                "center3D": {"x": 10.0, "y": 0.0, "z": 0.0},
                "size3D": {"x": 2.0, "y": 2.0, "z": 1.0},
                "rotation3D": {"x": 0.0, "y": 0.0, "z": 0.0}
            },
            {
                "objectId": "obj_2",
                "center3D": {"x": -5.0, "y": 3.0, "z": 1.0},
                "size3D": {"x": 1.0, "y": 1.0, "z": 2.0},
                "rotation3D": {"x": 0.0, "y": 0.0, "z": 1.57}  # 90度
            }
        ]
    }
    
    print("测试数据:")
    print(json.dumps(test_data, indent=2))
    
    # 预期结果：所有物体的X坐标应该减少0.5（因为自车向前0.5米）
    print("预期结果:")
    for obj in test_data["objects"]:
        original_x = obj["center3D"]["x"]
        expected_x = original_x - 0.5
        print(f"  {obj['objectId']}: X坐标从 {original_x} 变为 {expected_x}")

if __name__ == "__main__":
    test_static_object_with_ego_motion()
    test_no_ego_motion()
    test_multiple_objects()
    
    print(f"\n=== 使用说明 ===")
    print("1. 启动静止物体专用服务器:")
    print("   python app_static_only.py")
    print("2. 服务器将在端口5001运行")
    print("3. 取消注释测试代码中的API调用部分进行实际测试")
    print("4. 查看日志文件: static_object_tracking.log")
