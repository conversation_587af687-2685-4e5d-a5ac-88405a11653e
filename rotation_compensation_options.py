#!/usr/bin/env python3
"""
旋转补偿选项分析
"""

import numpy as np
from scipy.spatial.transform import Rotation as R

def analyze_rotation_compensation():
    """分析静止物体的旋转补偿问题"""
    
    print("=== 静止物体旋转补偿分析 ===")
    
    print("问题：位置对了，但旋转有点多")
    print("分析：对于静止物体，旋转补偿的逻辑与位置补偿不同")
    
    # 模拟场景
    ego_rotation_angle = np.radians(5)  # 自车左转5度
    ego_rotation_matrix = R.from_euler('z', ego_rotation_angle).as_matrix()
    
    # 物体原始旋转
    object_original_rotation = np.array([0.0, 0.0, -0.192])  # 原始旋转
    object_rotation_matrix = R.from_euler('xyz', object_original_rotation).as_matrix()
    
    print(f"\n场景设定：")
    print(f"自车左转: {np.degrees(ego_rotation_angle):.1f}度")
    print(f"物体原始旋转: {object_original_rotation}")
    
    print(f"\n=== 三种旋转处理方式 ===")
    
    # 方式1：不做旋转补偿（推荐用于静止物体）
    rotation_option1 = object_original_rotation.copy()
    print(f"方式1 - 保持原始旋转:")
    print(f"  最终旋转: {rotation_option1}")
    print(f"  理由: 静止物体本身没有旋转，只是观测角度变化")
    
    # 方式2：应用自车旋转补偿
    ego_rotation_euler = np.array([0.0, 0.0, ego_rotation_angle])
    compensated_rotation_matrix = np.dot(ego_rotation_matrix, object_rotation_matrix)
    rotation_option2 = R.from_matrix(compensated_rotation_matrix).as_euler('xyz')
    print(f"\n方式2 - 应用自车旋转补偿:")
    print(f"  最终旋转: {rotation_option2}")
    print(f"  理由: 补偿自车旋转对物体朝向的影响")
    
    # 方式3：应用逆旋转补偿
    ego_rotation_inv_matrix = np.linalg.inv(ego_rotation_matrix)
    inv_compensated_rotation_matrix = np.dot(ego_rotation_inv_matrix, object_rotation_matrix)
    rotation_option3 = R.from_matrix(inv_compensated_rotation_matrix).as_euler('xyz')
    print(f"\n方式3 - 应用逆旋转补偿:")
    print(f"  最终旋转: {rotation_option3}")
    print(f"  理由: 反向补偿自车旋转")
    
    print(f"\n=== 推荐方案 ===")
    print("对于静止物体，通常推荐方式1（保持原始旋转）")
    print("原因：")
    print("1. 静止物体本身没有旋转")
    print("2. 物体的朝向在世界坐标系中是固定的")
    print("3. 只有观测角度发生了变化，不是物体本身旋转")
    print("4. 过度的旋转补偿会导致物体朝向错误")

def create_rotation_options():
    """创建不同旋转选项的代码"""
    
    print(f"\n=== 代码选项 ===")
    
    print("当前代码（方式1 - 保持原始旋转）:")
    print("""
# 旋转处理：对于静止物体，通常不需要旋转补偿
compensated_rotation = bbox_rotation.copy()  # 保持原始旋转
    """)
    
    print("如果需要方式2（应用旋转补偿）:")
    print("""
# 应用自车旋转补偿
ego_rotation_matrix = icp_transform[:3, :3]
original_rotation_matrix = R.from_euler('xyz', bbox_rotation).as_matrix()
compensated_rotation_matrix = np.dot(ego_rotation_matrix, original_rotation_matrix)
compensated_rotation = R.from_matrix(compensated_rotation_matrix).as_euler('xyz')
    """)
    
    print("如果需要方式3（应用逆旋转补偿）:")
    print("""
# 应用逆旋转补偿
ego_rotation_inv_matrix = np.linalg.inv(icp_transform[:3, :3])
original_rotation_matrix = R.from_euler('xyz', bbox_rotation).as_matrix()
compensated_rotation_matrix = np.dot(ego_rotation_inv_matrix, original_rotation_matrix)
compensated_rotation = R.from_matrix(compensated_rotation_matrix).as_euler('xyz')
    """)

def suggest_solution():
    """建议解决方案"""
    
    print(f"\n=== 解决方案建议 ===")
    
    print("当前状态：")
    print("✅ 位置补偿正确（方法A）")
    print("❌ 旋转有点多")
    
    print("解决方案：")
    print("1. 当前已修改为保持原始旋转（推荐）")
    print("2. 测试看是否解决了旋转过多的问题")
    print("3. 如果还有问题，可能需要微调旋转补偿量")
    
    print("测试步骤：")
    print("1. 重启服务器: python app_final_fix.py")
    print("2. 发送API请求")
    print("3. 观察旋转是否还过多")
    print("4. 如果需要，可以尝试其他旋转选项")

if __name__ == "__main__":
    analyze_rotation_compensation()
    create_rotation_options()
    suggest_solution()
    
    print(f"\n=== 总结 ===")
    print("问题：位置对了，旋转多了")
    print("修复：改为保持原始旋转（静止物体不应该旋转）")
    print("测试：重新运行看旋转是否正常")
    print("备选：如果需要，可以尝试其他旋转补偿方式")
