#!/usr/bin/env python3
"""
测试左转修复
"""

import numpy as np
from scipy.spatial.transform import Rotation as R

def test_left_turn_scenario():
    """测试左转场景"""
    
    print("=== 测试左转场景修复 ===")
    
    # 模拟自车左转的情况
    print("场景：自车左转（逆时针旋转）")
    
    # 自车左转5度的真实运动
    true_ego_rotation = np.radians(5)  # 正值表示逆时针（左转）
    true_ego_translation = np.array([0.8, 0.0, 0.0])  # 向前0.8米
    
    print(f"真实自车运动：")
    print(f"  平移: {true_ego_translation}")
    print(f"  旋转: {true_ego_rotation:.4f}弧度 = {np.degrees(true_ego_rotation):.1f}度（左转）")
    
    # 构建真实自车变换矩阵
    true_rotation_matrix = R.from_euler('z', true_ego_rotation).as_matrix()
    true_ego_transform = np.eye(4)
    true_ego_transform[:3, :3] = true_rotation_matrix
    true_ego_transform[:3, 3] = true_ego_translation
    
    print(f"\n真实自车变换矩阵:")
    for i, row in enumerate(true_ego_transform):
        print(f"  [{i}] {row}")
    
    # 模拟ICP可能给出的结果（从PCD1到PCD2的变换）
    # 这通常是自车运动的逆
    icp_transform = np.linalg.inv(true_ego_transform)
    
    print(f"\nICP可能给出的变换（PCD1->PCD2）:")
    for i, row in enumerate(icp_transform):
        print(f"  [{i}] {row}")
    
    # 测试静止物体
    static_object = np.array([14.3054, 2.9938, 3.9605])
    static_object_homo = np.append(static_object, 1.0)
    
    print(f"\n静止物体原始位置: {static_object}")
    
    # 方法1：直接使用ICP结果（错误的方法）
    wrong_ego_inv = np.linalg.inv(icp_transform)
    wrong_result = wrong_ego_inv @ static_object_homo
    wrong_change = wrong_result[:3] - static_object
    
    print(f"\n方法1 - 直接使用ICP结果（可能错误）:")
    print(f"  新位置: {wrong_result[:3]}")
    print(f"  位置变化: {wrong_change}")
    print(f"  Y变化: {wrong_change[1]:.4f} ({'右偏' if wrong_change[1] > 0 else '左偏'})")
    
    # 方法2：使用ICP的逆作为自车变换（修正的方法）
    corrected_ego_transform = icp_transform  # 直接使用ICP结果作为自车逆变换
    corrected_result = corrected_ego_transform @ static_object_homo
    corrected_change = corrected_result[:3] - static_object
    
    print(f"\n方法2 - 使用ICP逆变换（修正方法）:")
    print(f"  新位置: {corrected_result[:3]}")
    print(f"  位置变化: {corrected_change}")
    print(f"  Y变化: {corrected_change[1]:.4f} ({'右偏' if corrected_change[1] > 0 else '左偏'})")
    
    # 方法3：使用真实自车变换的逆（理论正确结果）
    true_ego_inv = np.linalg.inv(true_ego_transform)
    true_result = true_ego_inv @ static_object_homo
    true_change = true_result[:3] - static_object
    
    print(f"\n方法3 - 理论正确结果:")
    print(f"  新位置: {true_result[:3]}")
    print(f"  位置变化: {true_change}")
    print(f"  Y变化: {true_change[1]:.4f} ({'右偏' if true_change[1] > 0 else '左偏'})")
    
    print(f"\n=== 预期行为分析 ===")
    print("自车左转时，静止物体应该：")
    print("  - 相对于自车向右偏移（Y坐标增加）")
    print("  - 这是因为自车向左转，物体相对位置向右")
    
    # 检查哪个方法给出了正确的方向
    print(f"\n=== 结果分析 ===")
    if true_change[1] > 0:
        print("✅ 理论结果：物体向右偏移（正确）")
        if corrected_change[1] > 0:
            print("✅ 修正方法：物体向右偏移（正确）")
        else:
            print("❌ 修正方法：物体向左偏移（错误）")
        
        if wrong_change[1] > 0:
            print("❌ 原方法：物体向右偏移（可能是巧合）")
        else:
            print("❌ 原方法：物体向左偏移（错误）")

def create_test_api_call():
    """创建测试API调用"""
    
    print(f"\n=== 测试API调用 ===")
    
    test_data = {
        "pcd1Url": "path/to/your/frame1.pcd",
        "pcd2Url": "path/to/your/frame2.pcd",
        "objects": [
            {
                "objectId": "left_turn_test",
                "center3D": {"x": 14.3054, "y": 2.9938, "z": 3.9605},
                "size3D": {"x": 6.7784, "y": 3.0099, "z": 7.9209},
                "rotation3D": {"x": 0.0, "y": 0.0, "z": -0.192}
            }
        ]
    }
    
    print("测试数据（左转场景）:")
    import json
    print(json.dumps(test_data, indent=2))
    
    print(f"\n预期结果（修复后）:")
    print("- 自车左转被正确检测")
    print("- 静止物体位置向右偏移（Y坐标增加）")
    print("- 不再出现方向相反的问题")

if __name__ == "__main__":
    test_left_turn_scenario()
    create_test_api_call()
    
    print(f"\n=== 修复总结 ===")
    print("问题：ICP变换方向与自车运动方向相反")
    print("修复：使用ICP变换的逆作为自车变换")
    print("原理：ICP给出PCD1->PCD2，我们需要自车运动（相反方向）")
    
    print(f"\n=== 下一步 ===")
    print("1. 重启修复后的服务器: python app_fixed.py")
    print("2. 重新发送API请求")
    print("3. 观察静止物体是否向右偏移（符合左转预期）")
    print("4. 如果还是不对，可能需要进一步调整坐标系")
