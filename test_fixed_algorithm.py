#!/usr/bin/env python3
"""
测试修复后的算法逻辑
"""

import numpy as np
from scipy.spatial.transform import Rotation as R

def test_ego_motion_logic():
    """测试自车运动补偿的数学逻辑"""
    
    print("=== 测试自车运动补偿逻辑 ===")
    
    # 模拟自车运动：向前1米，向右转5度
    ego_translation = np.array([1.0, 0.0, 0.0])
    ego_rotation = np.array([0.0, 0.0, np.radians(5)])
    
    # 构建自车变换矩阵
    ego_rotation_matrix = R.from_euler('xyz', ego_rotation).as_matrix()
    ego_transform = np.eye(4)
    ego_transform[:3, :3] = ego_rotation_matrix
    ego_transform[:3, 3] = ego_translation
    
    print(f"自车变换矩阵:")
    print(ego_transform)
    
    # 测试静止物体
    print(f"\n--- 静止物体测试 ---")
    static_object_pos = np.array([5.0, 2.0, 0.0])  # 前方5米，右侧2米
    static_object_rot = np.array([0.0, 0.0, 0.0])
    
    print(f"静止物体原始位置: {static_object_pos}")
    
    # 对静止物体应用自车运动的逆变换
    ego_transform_inv = np.linalg.inv(ego_transform)
    static_pos_homo = np.append(static_object_pos, 1.0)
    static_new_pos_homo = np.dot(ego_transform_inv, static_pos_homo)
    static_new_pos = static_new_pos_homo[:3]
    
    print(f"静止物体新位置: {static_new_pos}")
    print(f"位置变化: {static_new_pos - static_object_pos}")
    
    # 测试运动物体
    print(f"\n--- 运动物体测试 ---")
    moving_object_pos = np.array([3.0, -1.0, 0.0])  # 前方3米，左侧1米
    
    # 模拟物体自身运动：向左移动0.5米
    object_motion = np.array([0.0, 0.5, 0.0])
    object_motion_matrix = np.eye(4)
    object_motion_matrix[:3, 3] = object_motion
    
    # ICP会检测到的总变换 = 自车运动 + 物体运动
    T_total = np.dot(ego_transform, object_motion_matrix)
    
    print(f"物体原始位置: {moving_object_pos}")
    print(f"物体自身运动: {object_motion}")
    print(f"ICP检测到的总变换:")
    print(T_total)
    
    # 分离物体运动
    T_object = np.dot(T_total, ego_transform_inv)
    print(f"分离出的物体运动:")
    print(T_object)
    
    # 应用物体运动到原始位置
    moving_pos_homo = np.append(moving_object_pos, 1.0)
    moving_new_pos_homo = np.dot(T_object, moving_pos_homo)
    moving_new_pos = moving_new_pos_homo[:3]
    
    print(f"运动物体新位置: {moving_new_pos}")
    print(f"位置变化: {moving_new_pos - moving_object_pos}")
    print(f"预期变化: {object_motion}")
    print(f"误差: {np.linalg.norm((moving_new_pos - moving_object_pos) - object_motion)}")

def test_api_request_format():
    """测试API请求格式"""
    
    print(f"\n=== API请求格式示例 ===")
    
    api_request = {
        "pcd1Url": "path/to/frame1.pcd",
        "pcd2Url": "path/to/frame2.pcd",
        "egoPose": {
            "translation": [1.0, 0.0, 0.0],  # 向前1米
            "rotation": [0.0, 0.0, 0.087]     # 向右转5度
        },
        "objects": [
            {
                "objectId": "static_obj",
                "center3D": {"x": 5.0, "y": 2.0, "z": 0.0},
                "size3D": {"x": 2.0, "y": 1.0, "z": 1.5},
                "rotation3D": {"x": 0.0, "y": 0.0, "z": 0.0}
            },
            {
                "objectId": "moving_obj", 
                "center3D": {"x": 3.0, "y": -1.0, "z": 0.0},
                "size3D": {"x": 1.5, "y": 1.5, "z": 2.0},
                "rotation3D": {"x": 0.0, "y": 0.0, "z": 0.0}
            }
        ]
    }
    
    import json
    print(json.dumps(api_request, indent=2))

def test_expected_behavior():
    """测试预期行为"""
    
    print(f"\n=== 预期行为说明 ===")
    print("""
    修复后的算法应该表现如下：
    
    1. 静止物体：
       - 检测为静止（点云重心距离小）
       - 直接应用自车运动的逆变换
       - 位置变化应该反映自车运动的相反方向
       - 例如：自车向前1米 → 静止物体相对位置向后约1米
    
    2. 运动物体：
       - 检测为运动（点云重心距离大）
       - 执行ICP配准得到总变换
       - 分离自车运动，得到物体真实运动
       - 位置变化应该只反映物体自身的运动
    
    3. 关键改进：
       - 所有物体都会考虑自车运动
       - 静止物体不再保持原位不变
       - 运动物体的运动不再包含自车运动成分
    """)

if __name__ == "__main__":
    test_ego_motion_logic()
    test_api_request_format()
    test_expected_behavior()
