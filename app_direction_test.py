#!/usr/bin/env python3
"""
方向测试版本 - 同时输出两种方向的结果
"""

from flask import Flask, request, jsonify
import numpy as np
from scipy.spatial.transform import Rotation as R
import open3d as o3d
import tempfile
import requests
import os
import time
import shutil
import traceback
import logging
from datetime import datetime
import uuid
import json

app = Flask(__name__)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('direction_test.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# 全局变量
current_request_id = None

def log_with_timestamp(message, level="INFO", obj_id=None):
    """带时间戳的日志输出"""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]
    prefix = f"[{timestamp}]"
    if current_request_id:
        prefix += f"[REQ:{current_request_id[:8]}]"
    if obj_id:
        prefix += f"[OBJ:{obj_id}]"
    
    full_message = f"{prefix} {message}"
    
    if level == "ERROR":
        logger.error(full_message)
    elif level == "WARNING":
        logger.warning(full_message)
    else:
        logger.info(full_message)
    
    print(full_message)

def log_matrix(matrix, name, obj_id=None):
    """记录矩阵数据"""
    log_with_timestamp(f"{name}:", obj_id=obj_id)
    for i, row in enumerate(matrix):
        row_str = " ".join([f"{val:8.4f}" for val in row])
        log_with_timestamp(f"  [{i}] [{row_str}]", obj_id=obj_id)

def log_vector(vector, name, obj_id=None):
    """记录向量数据"""
    vector_str = " ".join([f"{val:8.4f}" for val in vector])
    log_with_timestamp(f"{name}: [{vector_str}]", obj_id=obj_id)

def parse_ego_pose(ego_pose_data):
    """解析自车位姿数据"""
    try:
        if "transform_matrix" in ego_pose_data:
            return np.array(ego_pose_data["transform_matrix"])
        
        elif "translation" in ego_pose_data and "rotation" in ego_pose_data:
            translation = np.array(ego_pose_data["translation"])
            rotation = np.array(ego_pose_data["rotation"])
            
            rotation_matrix = R.from_euler('xyz', rotation).as_matrix()
            transform = np.eye(4)
            transform[:3, :3] = rotation_matrix
            transform[:3, 3] = translation
            return transform
        
        else:
            log_with_timestamp("Unknown ego pose data format", "ERROR")
            return None
            
    except Exception as e:
        log_with_timestamp(f"Failed to parse ego pose: {e}", "ERROR")
        return None

def download_file_from_url(url, temp_dir):
    """下载文件"""
    log_with_timestamp(f"Starting download: {url}")
    
    headers = {'User-Agent': 'Mozilla/5.0 (compatible; FileDownloader/1.0)'}

    try:
        download_start = time.time()
        with requests.get(url, stream=True, timeout=30, headers=headers) as response:
            response.raise_for_status()
            
            suffix = ".pcd"
            temp_file = tempfile.NamedTemporaryFile(dir=temp_dir, suffix=suffix, delete=False)

            with open(temp_file.name, 'wb') as f:
                shutil.copyfileobj(response.raw, f)
            
            download_end = time.time()
            download_time = download_end - download_start
            file_size = os.path.getsize(temp_file.name) / 1024 / 1024  # MB
            
            log_with_timestamp(f"Download completed: {temp_file.name}")
            log_with_timestamp(f"Download time: {download_time:.3f}s, size: {file_size:.2f}MB")
            
            return temp_file.name

    except Exception as e:
        error_msg = f"Download failed {url}: {e}"
        log_with_timestamp(error_msg, "ERROR")
        raise RuntimeError(error_msg)

def estimate_ego_motion_simple(path1, path2):
    """简化的自车运动估计"""
    try:
        log_with_timestamp("=== Starting simplified ego motion estimation ===")
        
        # 加载点云
        pcd1 = o3d.io.read_point_cloud(path1)
        pcd2 = o3d.io.read_point_cloud(path2)
        
        log_with_timestamp(f"Loaded point clouds - PCD1: {len(pcd1.points)} points, PCD2: {len(pcd2.points)} points")
        
        if len(pcd1.points) == 0 or len(pcd2.points) == 0:
            log_with_timestamp("Empty point cloud detected, using identity matrix", "WARNING")
            return np.eye(4)
        
        # 简化的下采样和配准
        voxel_size = 0.5
        pcd1_down = pcd1.voxel_down_sample(voxel_size)
        pcd2_down = pcd2.voxel_down_sample(voxel_size)
        
        log_with_timestamp(f"After downsampling - PCD1: {len(pcd1_down.points)} points, PCD2: {len(pcd2_down.points)} points")
        
        # 简单的ICP配准
        distance_threshold = voxel_size * 2
        result = o3d.pipelines.registration.registration_icp(
            pcd1_down, pcd2_down, distance_threshold, np.eye(4),
            o3d.pipelines.registration.TransformationEstimationPointToPoint())
        
        log_with_timestamp(f"ICP completed - Fitness: {result.fitness:.4f}, RMSE: {result.inlier_rmse:.4f}")
        
        return result.transformation
        
    except Exception as e:
        log_with_timestamp(f"Failed to estimate ego motion: {e}", "ERROR")
        return np.eye(4)

def process_object_both_directions(path1, path2, bbox_center, bbox_size, bbox_rotation, ego_transform, obj_id):
    """处理物体 - 测试两个方向"""
    
    log_with_timestamp("=== Processing object with direction test ===", obj_id=obj_id)
    log_vector(bbox_center, "Input center", obj_id)
    
    start_time = time.time()
    
    try:
        # 如果没有自车变换，估计一个
        if ego_transform is None:
            log_with_timestamp("No ego transform provided, estimating from point clouds", obj_id=obj_id)
            icp_transform = estimate_ego_motion_simple(path1, path2)
        else:
            icp_transform = ego_transform
        
        log_matrix(icp_transform, "ICP transform", obj_id)
        
        # 测试两个方向
        original_center = np.array(bbox_center)
        original_center_homo = np.append(original_center, 1.0)
        
        # 方向1：直接使用ICP结果的逆
        ego_inv_1 = np.linalg.inv(icp_transform)
        result_1_homo = np.dot(ego_inv_1, original_center_homo)
        result_1 = result_1_homo[:3]
        change_1 = result_1 - original_center
        
        # 方向2：直接使用ICP结果
        result_2_homo = np.dot(icp_transform, original_center_homo)
        result_2 = result_2_homo[:3]
        change_2 = result_2 - original_center
        
        log_with_timestamp("=== Direction Test Results ===", obj_id=obj_id)
        log_with_timestamp("Direction 1 (using ICP inverse):", obj_id=obj_id)
        log_vector(result_1, "  New position", obj_id)
        log_vector(change_1, "  Position change", obj_id)
        log_with_timestamp(f"  Y change: {change_1[1]:.4f} ({'right' if change_1[1] > 0 else 'left'})", obj_id=obj_id)
        
        log_with_timestamp("Direction 2 (using ICP direct):", obj_id=obj_id)
        log_vector(result_2, "  New position", obj_id)
        log_vector(change_2, "  Position change", obj_id)
        log_with_timestamp(f"  Y change: {change_2[1]:.4f} ({'right' if change_2[1] > 0 else 'left'})", obj_id=obj_id)
        
        # 默认返回方向1的结果
        result = {
            "size3D": {
                "x": float(bbox_size[0]),
                "y": float(bbox_size[1]),
                "z": float(bbox_size[2]),
            },
            "center3D": {
                "x": float(result_1[0]),
                "y": float(result_1[1]),
                "z": float(result_1[2]),
            },
            "rotation3D": {
                "x": float(bbox_rotation[0]),
                "y": float(bbox_rotation[1]),
                "z": float(bbox_rotation[2]),
            },
            "points": 0,
            "direction_test": {
                "direction_1": {
                    "center": [float(result_1[0]), float(result_1[1]), float(result_1[2])],
                    "change": [float(change_1[0]), float(change_1[1]), float(change_1[2])],
                    "y_direction": "right" if change_1[1] > 0 else "left"
                },
                "direction_2": {
                    "center": [float(result_2[0]), float(result_2[1]), float(result_2[2])],
                    "change": [float(change_2[0]), float(change_2[1]), float(change_2[2])],
                    "y_direction": "right" if change_2[1] > 0 else "left"
                }
            }
        }
        
        processing_time = time.time() - start_time
        log_with_timestamp(f"Object processing completed in {processing_time:.3f}s", obj_id=obj_id)
        
        return result
        
    except Exception as e:
        log_with_timestamp(f"Object processing failed: {e}", "ERROR", obj_id)
        raise

@app.route('/predict', methods=['POST'])
def predict():
    global current_request_id
    current_request_id = str(uuid.uuid4())
    
    log_with_timestamp("=== New prediction request (direction test version) ===")
    
    try:
        data = request.get_json()

        # Get parameters
        path1 = data.get('pcd1Url')
        path2 = data.get('pcd2Url')
        objects = data.get('objects', [])
        
        log_with_timestamp(f"Point cloud file 1: {path1}")
        log_with_timestamp(f"Point cloud file 2: {path2}")
        log_with_timestamp(f"Number of objects: {len(objects)}")
        
        # Get ego pose information
        ego_pose_data = data.get('egoPose', None)
        ego_transform = None
        if ego_pose_data:
            ego_transform = parse_ego_pose(ego_pose_data)
        
        # Create temporary directory and download files
        with tempfile.TemporaryDirectory() as temp_dir:
            download_start_time = time.time()
            
            path1 = download_file_from_url(path1, temp_dir)
            path2 = download_file_from_url(path2, temp_dir)
            
            download_end_time = time.time()
            download_duration = download_end_time - download_start_time
            
            # Process all objects
            results = []
            processing_start_time = time.time()
            
            for obj in objects:
                obj_id = obj.get("objectId", "unknown")
                size3D = obj.get("size3D", {})
                center3D = obj.get("center3D", {})
                rotation3D = obj.get("rotation3D", {})

                bbox_center = [center3D.get("x", 0), center3D.get("y", 0), center3D.get("z", 0)]
                bbox_size = [size3D.get("x", 1), size3D.get("y", 1), size3D.get("z", 1)]
                bbox_rotation = [rotation3D.get("x", 0), rotation3D.get("y", 0), rotation3D.get("z", 0)]
                
                try:
                    result = process_object_both_directions(path1, path2, bbox_center, bbox_size, bbox_rotation, ego_transform, obj_id)
                    result["objectId"] = obj_id
                    results.append(result)
                    
                except Exception as e:
                    log_with_timestamp(f"Object {obj_id} processing failed: {e}", "ERROR", obj_id)
                    result = {
                        "objectId": obj_id,
                        "size3D": size3D,
                        "center3D": center3D,
                        "rotation3D": rotation3D,
                        "points": 0,
                        "error": str(e)
                    }
                    results.append(result)
            
            processing_end_time = time.time()
            processing_duration = processing_end_time - processing_start_time
            
            total_duration = processing_end_time - download_start_time
            log_with_timestamp(f"=== Prediction request completed in {total_duration:.3f}s ===")
            
            return jsonify({
                "status": "success",
                "result": results,
                "request_id": current_request_id,
                "processing_time": {
                    "download": download_duration,
                    "processing": processing_duration,
                    "total": total_duration
                },
                "version": "direction_test",
                "note": "Check direction_test field in each result to see both directions",
                "timestamp": datetime.now().isoformat()
            })
            
    except Exception as e:
        error_msg = f"Error during processing: {str(e)}"
        log_with_timestamp(error_msg, "ERROR")
        
        return jsonify({
            "status": "fail",
            "result": error_msg,
            "request_id": current_request_id,
            "timestamp": datetime.now().isoformat()
        })

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5003, debug=True)
