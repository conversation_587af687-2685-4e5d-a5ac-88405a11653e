#!/usr/bin/env python3
"""
简化版本：只处理静止物体的算法
用于调试自车运动补偿
"""

from flask import Flask, request, jsonify
import numpy as np
from scipy.spatial.transform import Rotation as R
import open3d as o3d
import tempfile
import requests
import os
import time
import shutil
import traceback
import logging
from datetime import datetime
import uuid
import json

app = Flask(__name__)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('static_object_tracking.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# 全局变量
current_request_id = None

def log_with_timestamp(message, level="INFO", obj_id=None):
    """带时间戳的日志输出"""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]
    prefix = f"[{timestamp}]"
    if current_request_id:
        prefix += f"[REQ:{current_request_id[:8]}]"
    if obj_id:
        prefix += f"[OBJ:{obj_id}]"
    
    full_message = f"{prefix} {message}"
    
    if level == "ERROR":
        logger.error(full_message)
    elif level == "WARNING":
        logger.warning(full_message)
    else:
        logger.info(full_message)
    
    print(full_message)

def log_matrix(matrix, name, obj_id=None):
    """记录矩阵数据"""
    log_with_timestamp(f"{name}:", obj_id=obj_id)
    for i, row in enumerate(matrix):
        row_str = " ".join([f"{val:8.4f}" for val in row])
        log_with_timestamp(f"  [{i}] [{row_str}]", obj_id=obj_id)

def log_vector(vector, name, obj_id=None):
    """记录向量数据"""
    vector_str = " ".join([f"{val:8.4f}" for val in vector])
    log_with_timestamp(f"{name}: [{vector_str}]", obj_id=obj_id)

def parse_ego_pose(ego_pose_data):
    """解析自车位姿数据"""
    try:
        if "transform_matrix" in ego_pose_data:
            return np.array(ego_pose_data["transform_matrix"])
        
        elif "translation" in ego_pose_data and "rotation" in ego_pose_data:
            translation = np.array(ego_pose_data["translation"])
            rotation = np.array(ego_pose_data["rotation"])
            
            rotation_matrix = R.from_euler('xyz', rotation).as_matrix()
            transform = np.eye(4)
            transform[:3, :3] = rotation_matrix
            transform[:3, 3] = translation
            return transform
        
        else:
            log_with_timestamp("未识别的自车位姿数据格式", "ERROR")
            return None
            
    except Exception as e:
        log_with_timestamp(f"解析自车位姿失败: {e}", "ERROR")
        return None

def download_file_from_url(url, temp_dir):
    """下载文件"""
    log_with_timestamp(f"开始下载文件: {url}")
    
    headers = {'User-Agent': 'Mozilla/5.0 (compatible; FileDownloader/1.0)'}

    try:
        download_start = time.time()
        with requests.get(url, stream=True, timeout=30, headers=headers) as response:
            response.raise_for_status()
            
            suffix = ".pcd"
            temp_file = tempfile.NamedTemporaryFile(dir=temp_dir, suffix=suffix, delete=False)

            with open(temp_file.name, 'wb') as f:
                shutil.copyfileobj(response.raw, f)
            
            download_end = time.time()
            download_time = download_end - download_start
            file_size = os.path.getsize(temp_file.name) / 1024 / 1024  # MB
            
            log_with_timestamp(f"文件下载完成: {temp_file.name}")
            log_with_timestamp(f"下载耗时: {download_time:.3f}秒, 文件大小: {file_size:.2f}MB")
            
            return temp_file.name

    except Exception as e:
        error_msg = f"下载失败 {url}: {e}"
        log_with_timestamp(error_msg, "ERROR")
        raise RuntimeError(error_msg)

def process_static_object(path1, path2, bbox_center, bbox_size, bbox_rotation, ego_transform, obj_id):
    """处理静止物体"""
    
    log_with_timestamp("=== 开始处理静止物体 ===", obj_id=obj_id)
    log_vector(bbox_center, "输入中心位置", obj_id)
    log_vector(bbox_size, "输入框大小", obj_id)
    log_vector(bbox_rotation, "输入旋转角度", obj_id)
    
    start_time = time.time()
    
    try:
        # 加载点云
        pcd1 = o3d.io.read_point_cloud(path1)
        pcd2 = o3d.io.read_point_cloud(path2)
        log_with_timestamp(f"点云加载成功 - PCD1: {len(pcd1.points)}点, PCD2: {len(pcd2.points)}点", obj_id=obj_id)
        
        # 如果没有自车变换，创建单位矩阵（无运动）
        if ego_transform is None:
            log_with_timestamp("未提供自车变换，假设自车无运动", obj_id=obj_id)
            ego_transform = np.eye(4)
        
        log_matrix(ego_transform, "自车变换矩阵", obj_id)
        
        # 计算自车运动补偿
        log_with_timestamp("开始计算自车运动补偿", obj_id=obj_id)
        
        # 计算逆变换矩阵
        ego_transform_inv = np.linalg.inv(ego_transform)
        log_matrix(ego_transform_inv, "自车逆变换矩阵", obj_id)
        
        # 应用逆变换到物体位置
        bbox_center_homo = np.append(bbox_center, 1.0)
        log_vector(bbox_center_homo, "原始中心齐次坐标", obj_id)
        
        new_center_homo = np.dot(ego_transform_inv, bbox_center_homo)
        log_vector(new_center_homo, "变换后齐次坐标", obj_id)
        
        new_center = new_center_homo[:3]
        
        log_vector(bbox_center, "原始中心", obj_id)
        log_vector(new_center, "变换后中心", obj_id)
        
        position_change = new_center - np.array(bbox_center)
        log_vector(position_change, "位置变化", obj_id)
        log_with_timestamp(f"位置变化距离: {np.linalg.norm(position_change):.4f}m", obj_id=obj_id)
        
        # 旋转变换
        log_with_timestamp("开始计算旋转变换", obj_id=obj_id)
        ego_rotation = ego_transform_inv[:3, :3]
        log_matrix(ego_rotation, "自车旋转逆变换", obj_id)
        
        original_rotation_matrix = R.from_euler('xyz', bbox_rotation).as_matrix()
        log_matrix(original_rotation_matrix, "原始旋转矩阵", obj_id)
        
        new_rotation_matrix = np.dot(ego_rotation, original_rotation_matrix)
        log_matrix(new_rotation_matrix, "变换后旋转矩阵", obj_id)
        
        new_euler = R.from_matrix(new_rotation_matrix).as_euler('xyz')
        
        log_vector(bbox_rotation, "原始旋转", obj_id)
        log_vector(new_euler, "变换后旋转", obj_id)
        
        rotation_change = new_euler - np.array(bbox_rotation)
        log_vector(rotation_change, "旋转变化", obj_id)
        
        # 构建结果
        result = {
            "size3D": {
                "x": float(bbox_size[0]),
                "y": float(bbox_size[1]),
                "z": float(bbox_size[2]),
            },
            "center3D": {
                "x": float(new_center[0]),
                "y": float(new_center[1]),
                "z": float(new_center[2]),
            },
            "rotation3D": {
                "x": float(new_euler[0]),
                "y": float(new_euler[1]),
                "z": float(new_euler[2]),
            },
            "points": 0,  # 简化版本不统计点数
            "is_stationary": True,
            "ego_compensated": True
        }
        
        processing_time = time.time() - start_time
        log_with_timestamp(f"静止物体处理完成，耗时: {processing_time:.3f}秒", obj_id=obj_id)
        
        # 最终验证
        final_position_change = np.array([result["center3D"]["x"], result["center3D"]["y"], result["center3D"]["z"]]) - np.array(bbox_center)
        log_vector(final_position_change, "最终位置变化", obj_id)
        log_with_timestamp(f"最终位置变化距离: {np.linalg.norm(final_position_change):.4f}m", obj_id=obj_id)
        
        return result
        
    except Exception as e:
        log_with_timestamp(f"静止物体处理失败: {e}", "ERROR", obj_id)
        log_with_timestamp(f"错误详情: {traceback.format_exc()}", "ERROR", obj_id)
        raise

@app.route('/predict', methods=['POST'])
def predict():
    global current_request_id
    current_request_id = str(uuid.uuid4())
    
    log_with_timestamp("=== 新的预测请求开始（静止物体专用版本）===")
    
    try:
        data = request.get_json()
        log_with_timestamp(f"接收到请求数据: {json.dumps(data, indent=2)}")

        # 获取参数
        path1 = data.get('pcd1Url')
        path2 = data.get('pcd2Url')
        objects = data.get('objects', [])
        
        log_with_timestamp(f"点云文件1: {path1}")
        log_with_timestamp(f"点云文件2: {path2}")
        log_with_timestamp(f"物体数量: {len(objects)}")
        
        # 获取自车位姿信息
        ego_pose_data = data.get('egoPose', None)
        ego_transform = None
        if ego_pose_data:
            log_with_timestamp(f"自车位姿数据: {json.dumps(ego_pose_data, indent=2)}")
            ego_transform = parse_ego_pose(ego_pose_data)
            if ego_transform is not None:
                log_matrix(ego_transform, "解析的自车变换矩阵")
            else:
                log_with_timestamp("自车位姿解析失败", "ERROR")
        
        # 创建临时目录并下载文件
        with tempfile.TemporaryDirectory() as temp_dir:
            download_start_time = time.time()
            log_with_timestamp("开始下载点云文件")
            
            path1 = download_file_from_url(path1, temp_dir)
            path2 = download_file_from_url(path2, temp_dir)
            
            download_end_time = time.time()
            download_duration = download_end_time - download_start_time
            log_with_timestamp(f"文件下载完成，总耗时: {download_duration:.3f}秒")
            
            # 处理所有物体（都按静止物体处理）
            results = []
            processing_start_time = time.time()
            
            for obj in objects:
                obj_id = obj.get("objectId", "unknown")
                size3D = obj.get("size3D", {})
                center3D = obj.get("center3D", {})
                rotation3D = obj.get("rotation3D", {})

                bbox_center = [center3D.get("x", 0), center3D.get("y", 0), center3D.get("z", 0)]
                bbox_size = [size3D.get("x", 1), size3D.get("y", 1), size3D.get("z", 1)]
                bbox_rotation = [rotation3D.get("x", 0), rotation3D.get("y", 0), rotation3D.get("z", 0)]
                
                try:
                    result = process_static_object(path1, path2, bbox_center, bbox_size, bbox_rotation, ego_transform, obj_id)
                    result["objectId"] = obj_id
                    results.append(result)
                    
                except Exception as e:
                    log_with_timestamp(f"物体 {obj_id} 处理失败: {e}", "ERROR", obj_id)
                    # 返回原始位置作为fallback
                    result = {
                        "objectId": obj_id,
                        "size3D": size3D,
                        "center3D": center3D,
                        "rotation3D": rotation3D,
                        "points": 0,
                        "error": str(e)
                    }
                    results.append(result)
            
            processing_end_time = time.time()
            processing_duration = processing_end_time - processing_start_time
            log_with_timestamp(f"所有物体处理完成，耗时: {processing_duration:.3f}秒")
            
            # 统计结果
            success_count = sum(1 for r in results if "error" not in r)
            error_count = len(results) - success_count
            
            log_with_timestamp(f"处理结果统计 - 成功: {success_count}, 错误: {error_count}")
            
            total_duration = processing_end_time - download_start_time
            log_with_timestamp(f"=== 预测请求完成，总耗时: {total_duration:.3f}秒 ===")
            
            return jsonify({
                "status": "success",
                "result": results,
                "request_id": current_request_id,
                "processing_time": {
                    "download": download_duration,
                    "processing": processing_duration,
                    "total": total_duration
                },
                "statistics": {
                    "total_objects": len(objects),
                    "success_objects": success_count,
                    "error_objects": error_count
                },
                "timestamp": datetime.now().isoformat(),
                "version": "static_only"
            })
            
    except Exception as e:
        error_msg = f"处理过程中发生错误: {str(e)}"
        log_with_timestamp(error_msg, "ERROR")
        log_with_timestamp(f"错误详情: {traceback.format_exc()}", "ERROR")
        
        return jsonify({
            "status": "fail",
            "result": error_msg,
            "request_id": current_request_id,
            "timestamp": datetime.now().isoformat()
        })

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5001, debug=True)
