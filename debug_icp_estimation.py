#!/usr/bin/env python3
"""
调试ICP估计问题
"""

import numpy as np
from scipy.spatial.transform import Rotation as R

def analyze_icp_problem():
    """分析ICP估计可能的问题"""
    
    print("=== ICP估计问题分析 ===")
    
    print("可能的问题：")
    print("1. ICP估计的变换矩阵接近单位矩阵")
    print("2. 点云质量不足，无法准确估计运动")
    print("3. 自车运动太小，在噪声范围内")
    print("4. ICP参数设置不当")
    print("5. 点云配准失败")
    
    print(f"\n如果ICP估计接近单位矩阵：")
    identity = np.eye(4)
    print("单位矩阵：")
    for i, row in enumerate(identity):
        print(f"  [{i}] {row}")
    
    # 测试接近单位矩阵的情况
    static_object = np.array([14.3054, 2.9938, 3.9605])
    static_object_homo = np.append(static_object, 1.0)
    
    # 如果ICP返回单位矩阵
    identity_result = np.dot(identity, static_object_homo)
    identity_change = identity_result[:3] - static_object
    
    print(f"\n如果ICP返回单位矩阵的结果：")
    print(f"原始位置: {static_object}")
    print(f"变换后位置: {identity_result[:3]}")
    print(f"位置变化: {identity_change}")
    print(f"变化距离: {np.linalg.norm(identity_change):.6f}m")
    
    print(f"\n这解释了为什么'框几乎没有变化'！")

def suggest_debugging_steps():
    """建议调试步骤"""
    
    print(f"\n=== 调试步骤建议 ===")
    
    print("1. 检查ICP估计质量：")
    print("   - 查看日志中的Fitness值（应该>0.3）")
    print("   - 查看RMSE值（应该<1.0）")
    print("   - 查看ICP变换矩阵是否接近单位矩阵")
    
    print("2. 检查点云质量：")
    print("   - 点云数量是否足够（>10000点）")
    print("   - 两个点云是否有足够重叠")
    print("   - 点云是否包含足够的几何特征")
    
    print("3. 检查自车运动：")
    print("   - 实际自车运动是否足够大")
    print("   - 运动是否在ICP能检测的范围内")
    print("   - 时间间隔是否合适")
    
    print("4. 调整ICP参数：")
    print("   - 减小voxel_size（提高精度）")
    print("   - 增加distance_threshold")
    print("   - 使用更复杂的配准算法")

def create_enhanced_icp_version():
    """创建增强的ICP版本"""
    
    print(f"\n=== 增强ICP版本建议 ===")
    
    enhanced_code = '''
def estimate_ego_motion_enhanced(path1, path2):
    """增强的自车运动估计"""
    try:
        log_with_timestamp("=== Enhanced ego motion estimation ===")
        
        # 加载点云
        pcd1 = o3d.io.read_point_cloud(path1)
        pcd2 = o3d.io.read_point_cloud(path2)
        
        log_with_timestamp(f"Original point clouds - PCD1: {len(pcd1.points)}, PCD2: {len(pcd2.points)}")
        
        # 检查点云质量
        if len(pcd1.points) < 1000 or len(pcd2.points) < 1000:
            log_with_timestamp("WARNING: Too few points for reliable ICP", "WARNING")
        
        # 更精细的下采样
        voxel_size = 0.1  # 减小体素大小
        pcd1_down = pcd1.voxel_down_sample(voxel_size)
        pcd2_down = pcd2.voxel_down_sample(voxel_size)
        
        log_with_timestamp(f"After fine downsampling - PCD1: {len(pcd1_down.points)}, PCD2: {len(pcd2_down.points)}")
        
        # 估计法线
        pcd1_down.estimate_normals()
        pcd2_down.estimate_normals()
        
        # 多尺度ICP
        distance_threshold = voxel_size * 2
        
        # 粗配准
        result_coarse = o3d.pipelines.registration.registration_icp(
            pcd1_down, pcd2_down, distance_threshold * 2, np.eye(4),
            o3d.pipelines.registration.TransformationEstimationPointToPoint())
        
        log_with_timestamp(f"Coarse ICP - Fitness: {result_coarse.fitness:.4f}, RMSE: {result_coarse.inlier_rmse:.4f}")
        
        # 精配准
        result_fine = o3d.pipelines.registration.registration_icp(
            pcd1_down, pcd2_down, distance_threshold, result_coarse.transformation,
            o3d.pipelines.registration.TransformationEstimationPointToPoint())
        
        log_with_timestamp(f"Fine ICP - Fitness: {result_fine.fitness:.4f}, RMSE: {result_fine.inlier_rmse:.4f}")
        
        # 检查变换质量
        transform = result_fine.transformation
        translation_norm = np.linalg.norm(transform[:3, 3])
        rotation_matrix = transform[:3, :3]
        rotation_angle = np.arccos((np.trace(rotation_matrix) - 1) / 2)
        
        log_with_timestamp(f"Estimated motion - Translation: {translation_norm:.4f}m, Rotation: {np.degrees(rotation_angle):.2f}deg")
        
        # 质量检查
        if result_fine.fitness < 0.1:
            log_with_timestamp("WARNING: Low ICP fitness, results may be unreliable", "WARNING")
        
        if translation_norm < 0.01 and rotation_angle < np.radians(0.5):
            log_with_timestamp("WARNING: Very small motion detected, may be noise", "WARNING")
        
        return transform
        
    except Exception as e:
        log_with_timestamp(f"Enhanced ICP failed: {e}", "ERROR")
        return np.eye(4)
    '''
    
    print("增强版本特性：")
    print("- 更精细的下采样（voxel_size=0.1）")
    print("- 多尺度ICP（粗配准+精配准）")
    print("- 详细的质量检查和警告")
    print("- 运动量检测和验证")

def check_coordinate_system():
    """检查坐标系问题"""
    
    print(f"\n=== 坐标系问题检查 ===")
    
    print("可能的坐标系问题：")
    print("1. 点云坐标系与期望不符")
    print("2. X/Y/Z轴定义与假设不同")
    print("3. 左手系vs右手系")
    print("4. 单位问题（米vs毫米）")
    
    print(f"\n建议验证：")
    print("1. 检查点云的坐标范围")
    print("2. 确认X轴是否为前进方向")
    print("3. 确认Y轴是否为左右方向")
    print("4. 确认坐标单位")

if __name__ == "__main__":
    analyze_icp_problem()
    suggest_debugging_steps()
    create_enhanced_icp_version()
    check_coordinate_system()
    
    print(f"\n=== 立即行动建议 ===")
    print("1. 先测试方法B（已切换）:")
    print("   python app_final_fix.py  # 端口5004")
    
    print("2. 检查日志中的关键信息:")
    print("   - ICP Fitness值")
    print("   - ICP RMSE值")
    print("   - ICP变换矩阵")
    print("   - 是否接近单位矩阵")
    
    print("3. 如果方法B也没效果:")
    print("   - 可能需要增强ICP算法")
    print("   - 或者检查点云质量")
    print("   - 或者确认坐标系定义")
    
    print("4. 请分享日志中的ICP结果:")
    print("   - Fitness和RMSE值")
    print("   - ICP变换矩阵的具体数值")
    print("   - 我可以进一步分析问题")
