#!/usr/bin/env python3
"""
测试带自车位姿的API调用
"""

import requests
import json
import numpy as np

def test_with_ego_pose():
    """测试提供自车位姿的情况"""
    
    print("=== 测试带自车位姿的API调用 ===")
    
    # 模拟自车运动：向前1米，向右转5度
    ego_translation = [1.0, 0.0, 0.0]  # 向前1米
    ego_rotation = [0.0, 0.0, np.radians(5)]  # 向右转5度（约0.087弧度）
    
    test_data = {
        "pcd1Url": "https://example.com/frame1.pcd",  # 这会失败，但可以看到逻辑
        "pcd2Url": "https://example.com/frame2.pcd",
        "egoPose": {
            "translation": ego_translation,
            "rotation": ego_rotation
        },
        "objects": [
            {
                "objectId": "test_with_ego_pose",
                "center3D": {"x": 14.3054, "y": 2.9938, "z": 3.9605},
                "size3D": {"x": 6.7784, "y": 3.0099, "z": 7.9209},
                "rotation3D": {"x": 0.0000, "y": 0.0000, "z": -0.1920}
            }
        ]
    }
    
    print("测试数据:")
    print(json.dumps(test_data, indent=2))
    
    # 计算预期结果
    print(f"\n=== 预期结果计算 ===")
    
    from scipy.spatial.transform import Rotation as R
    
    # 构建自车变换矩阵
    ego_rotation_matrix = R.from_euler('xyz', ego_rotation).as_matrix()
    ego_transform = np.eye(4)
    ego_transform[:3, :3] = ego_rotation_matrix
    ego_transform[:3, 3] = ego_translation
    
    print(f"自车变换矩阵:")
    for i, row in enumerate(ego_transform):
        print(f"  [{i}] {row}")
    
    # 计算逆变换
    ego_transform_inv = np.linalg.inv(ego_transform)
    print(f"自车逆变换矩阵:")
    for i, row in enumerate(ego_transform_inv):
        print(f"  [{i}] {row}")
    
    # 应用到静止物体
    original_center = np.array([14.3054, 2.9938, 3.9605])
    original_center_homo = np.append(original_center, 1.0)
    
    new_center_homo = np.dot(ego_transform_inv, original_center_homo)
    new_center = new_center_homo[:3]
    
    position_change = new_center - original_center
    
    print(f"原始位置: {original_center}")
    print(f"预期新位置: {new_center}")
    print(f"预期位置变化: {position_change}")
    print(f"预期位置变化距离: {np.linalg.norm(position_change):.4f}m")
    
    # 如果服务器在运行，可以取消注释进行实际测试
    """
    try:
        print(f"\n=== 发送API请求到端口5002 ===")
        response = requests.post(
            "http://localhost:5002/predict",
            json=test_data,
            timeout=60
        )
        
        print(f"响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("API响应:")
            print(json.dumps(result, indent=2))
            
            # 分析结果
            if result.get("status") == "success" and result.get("result"):
                obj_result = result["result"][0]
                actual_center = obj_result.get("center3D", {})
                actual_position = np.array([actual_center.get("x", 0), actual_center.get("y", 0), actual_center.get("z", 0)])
                actual_change = actual_position - original_center
                
                print(f"\n=== 结果对比 ===")
                print(f"预期位置: {new_center}")
                print(f"实际位置: {actual_position}")
                print(f"预期变化: {position_change}")
                print(f"实际变化: {actual_change}")
                print(f"误差: {np.linalg.norm(actual_change - position_change):.6f}m")
                
                if np.linalg.norm(actual_change - position_change) < 0.001:
                    print("✅ 结果正确！")
                else:
                    print("❌ 结果不匹配")
                
        else:
            print(f"API错误: {response.text}")
            
    except requests.exceptions.RequestException as e:
        print(f"请求失败: {e}")
    """

def test_without_ego_pose():
    """测试不提供自车位姿的情况（应该尝试估计）"""
    
    print(f"\n=== 测试不提供自车位姿（自动估计）===")
    
    test_data = {
        "pcd1Url": "https://example.com/frame1.pcd",
        "pcd2Url": "https://example.com/frame2.pcd",
        # 不提供egoPose，应该尝试从点云估计
        "objects": [
            {
                "objectId": "test_auto_estimate",
                "center3D": {"x": 14.3054, "y": 2.9938, "z": 3.9605},
                "size3D": {"x": 6.7784, "y": 3.0099, "z": 7.9209},
                "rotation3D": {"x": 0.0000, "y": 0.0000, "z": -0.1920}
            }
        ]
    }
    
    print("测试数据:")
    print(json.dumps(test_data, indent=2))
    print("预期行为: 算法应该尝试从点云估计自车运动")

def create_curl_command():
    """生成curl命令用于测试"""
    
    test_data = {
        "pcd1Url": "https://example.com/frame1.pcd",
        "pcd2Url": "https://example.com/frame2.pcd",
        "egoPose": {
            "translation": [1.0, 0.0, 0.0],
            "rotation": [0.0, 0.0, 0.087]
        },
        "objects": [
            {
                "objectId": "curl_test",
                "center3D": {"x": 14.3054, "y": 2.9938, "z": 3.9605},
                "size3D": {"x": 6.7784, "y": 3.0099, "z": 7.9209},
                "rotation3D": {"x": 0.0000, "y": 0.0000, "z": -0.1920}
            }
        ]
    }
    
    print(f"\n=== Curl命令 ===")
    print("curl -X POST http://localhost:5002/predict \\")
    print("  -H 'Content-Type: application/json' \\")
    print(f"  -d '{json.dumps(test_data)}'")

if __name__ == "__main__":
    test_with_ego_pose()
    test_without_ego_pose()
    create_curl_command()
    
    print(f"\n=== 使用说明 ===")
    print("1. 启动修复版服务器:")
    print("   python app_fixed.py")
    print("2. 取消注释测试代码中的API调用部分")
    print("3. 运行测试:")
    print("   python test_with_ego_pose.py")
    print("4. 或者使用生成的curl命令直接测试")
    print("5. 查看日志: tail -f fixed_object_tracking.log")
    
    print(f"\n=== 关键点 ===")
    print("- 必须提供egoPose参数才能看到位置变化")
    print("- 如果不提供，算法会尝试从点云估计自车运动")
    print("- 预期位置变化约0.9米（向后偏移）")
