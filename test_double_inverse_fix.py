#!/usr/bin/env python3
"""
测试双重逆变换修复
"""

import numpy as np
from scipy.spatial.transform import Rotation as R

def test_fix():
    """测试修复效果"""
    
    print("=== 测试双重逆变换修复 ===")
    
    # 自车左转5度，向前1米
    ego_translation = np.array([1.0, 0.0, 0.0])
    ego_rotation_angle = np.radians(5)  # 左转
    ego_rotation_matrix = R.from_euler('z', ego_rotation_angle).as_matrix()
    ego_transform = np.eye(4)
    ego_transform[:3, :3] = ego_rotation_matrix
    ego_transform[:3, 3] = ego_translation
    
    print(f"自车真实运动（左转5度，前进1米）:")
    for i, row in enumerate(ego_transform):
        print(f"  [{i}] {row}")
    
    # ICP应该给出的结果（自车运动的逆）
    icp_result = np.linalg.inv(ego_transform)
    print(f"\nICP给出的变换（自车运动的逆）:")
    for i, row in enumerate(icp_result):
        print(f"  [{i}] {row}")
    
    # 静止物体
    static_object = np.array([14.3054, 2.9938, 3.9605])
    static_object_homo = np.append(static_object, 1.0)
    
    print(f"\n静止物体原始位置: {static_object}")
    
    # 修复前的算法（错误）
    old_algorithm_transform = np.linalg.inv(icp_result)  # 双重逆变换
    old_result = np.dot(old_algorithm_transform, static_object_homo)
    old_change = old_result[:3] - static_object
    
    print(f"\n修复前算法（双重逆变换）:")
    print(f"  新位置: {old_result[:3]}")
    print(f"  位置变化: {old_change}")
    print(f"  Y变化: {old_change[1]:.4f} ({'右移' if old_change[1] > 0 else '左移'})")
    print(f"  问题: 过度补偿，向右偏移过多")
    
    # 修复后的算法（正确）
    new_algorithm_transform = icp_result  # 直接使用ICP结果
    new_result = np.dot(new_algorithm_transform, static_object_homo)
    new_change = new_result[:3] - static_object
    
    print(f"\n修复后算法（直接使用ICP结果）:")
    print(f"  新位置: {new_result[:3]}")
    print(f"  位置变化: {new_change}")
    print(f"  Y变化: {new_change[1]:.4f} ({'右移' if new_change[1] > 0 else '左移'})")
    
    # 分析结果
    print(f"\n=== 结果分析 ===")
    print(f"自车左转时，静止物体的预期行为:")
    print(f"  - 从自车视角看，物体应该相对向右偏移")
    print(f"  - 但在世界坐标系中，可能表现为向左偏移")
    print(f"  - 关键是偏移量要合理，不能过度")
    
    print(f"\n修复效果:")
    print(f"  修复前Y变化: {old_change[1]:.4f} (过度偏移)")
    print(f"  修复后Y变化: {new_change[1]:.4f} (合理偏移)")
    print(f"  改善程度: {abs(old_change[1]) - abs(new_change[1]):.4f}")
    
    if abs(new_change[1]) < abs(old_change[1]):
        print(f"  ✅ 修复成功：偏移量减少了")
    else:
        print(f"  ❌ 修复失败：偏移量没有改善")

def explain_coordinate_system():
    """解释坐标系问题"""
    
    print(f"\n=== 坐标系说明 ===")
    print("可能的坐标系定义:")
    print("1. 自车坐标系:")
    print("   - X轴：前方")
    print("   - Y轴：左侧（或右侧，取决于定义）")
    print("   - Z轴：上方")
    
    print("2. 世界坐标系:")
    print("   - 可能与自车坐标系不同")
    print("   - Y轴正方向可能是左或右")
    
    print("3. 重要的是:")
    print("   - 修复后偏移量应该合理")
    print("   - 不应该出现过度补偿")
    print("   - 静止物体应该跟随自车运动适度调整位置")

def create_test_api():
    """创建测试API调用"""
    
    print(f"\n=== 测试API调用 ===")
    
    test_data = {
        "pcd1Url": "path/to/your/frame1.pcd",
        "pcd2Url": "path/to/your/frame2.pcd",
        "objects": [
            {
                "objectId": "double_inverse_fix_test",
                "center3D": {"x": 14.3054, "y": 2.9938, "z": 3.9605},
                "size3D": {"x": 6.7784, "y": 3.0099, "z": 7.9209},
                "rotation3D": {"x": 0.0, "y": 0.0, "z": -0.192}
            }
        ]
    }
    
    import json
    print("测试数据:")
    print(json.dumps(test_data, indent=2))
    
    print(f"\n预期结果（修复后）:")
    print("- 静止物体位置变化应该合理")
    print("- 不再出现过度向右偏移")
    print("- Y方向变化应该在合理范围内")
    print("- 日志中会显示'修复：直接使用ICP结果，不再求逆'")

if __name__ == "__main__":
    test_fix()
    explain_coordinate_system()
    create_test_api()
    
    print(f"\n=== 修复总结 ===")
    print("问题：双重逆变换导致过度补偿")
    print("原因：ICP结果已经是逆变换，算法又求了一次逆")
    print("修复：直接使用ICP结果作为补偿变换")
    print("效果：消除过度偏移，位置变化更合理")
    
    print(f"\n=== 下一步 ===")
    print("1. 重启修复后的服务器: python app_fixed.py")
    print("2. 重新发送API请求")
    print("3. 观察静止物体位置变化是否更合理")
    print("4. 确认不再出现'往右多偏移'的问题")
