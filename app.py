from flask import Flask, request, jsonify
import mysql.connector
import json
import numpy as np
from scipy.spatial.transform import Rotation as R
import open3d as o3d
import tempfile
import requests
import os
import time
import shutil
import traceback
from concurrent.futures import ThreadPoolExecutor

app = Flask(__name__)

# 默认点云路径
DEFAULT_PCD1_PATH = "C:/wicv/example5/Scene_01/lidar_point_cloud_0/1744420861.599999.pcd"
DEFAULT_PCD2_PATH = "C:/wicv/example5/Scene_01/lidar_point_cloud_0/1744420861.699999.pcd"

# 数据库连接配置
db_config = {
    'host': 'localhost',
    'port': 8191,
    'user': 'xtreme1',
    'password': 'Rc4K3L6f',
    'database': 'xtreme1'
}


def get_contour_info_by_id(object_id):
    """从数据库中获取 contour 信息"""
    try:
        conn = mysql.connector.connect(**db_config)
        cursor = conn.cursor()

        query = "SELECT class_attributes FROM data_annotation_object WHERE id = %s"
        cursor.execute(query, (object_id,))
        result = cursor.fetchone()

        if not result:
            return None

        class_attributes = json.loads(result[0])
        contour_info = class_attributes.get("contour", None)

        if not contour_info:
            return {"error": f"No 'contour' field for id={object_id}"}

        return contour_info

    except mysql.connector.Error as err:
        return {"error": f"Database error: {err}"}
    except json.JSONDecodeError as jde:
        return {"error": f"JSON decode error: {jde}"}
    finally:
        if 'conn' in locals() and conn.is_connected():
            cursor.close()
            conn.close()


def preprocess_point_cloud(pcd, voxel_size):
    # 下采样
    pcd_down = pcd.voxel_down_sample(voxel_size)

    # 法线估计
    pcd_down.estimate_normals(o3d.geometry.KDTreeSearchParamHybrid(radius=voxel_size * 2, max_nn=30))

    # FPFH 特征
    fpfh = o3d.pipelines.registration.compute_fpfh_feature(
        pcd_down,
        o3d.geometry.KDTreeSearchParamHybrid(radius=voxel_size * 5, max_nn=100)
    )
    return pcd_down, fpfh


def execute_global_registration(source_down, target_down, source_fpfh, target_fpfh, voxel_size):
    distance_threshold = voxel_size * 1.5
    result = o3d.pipelines.registration.registration_ransac_based_on_feature_matching(
        source_down, target_down, source_fpfh, target_fpfh,
        mutual_filter=True,
        max_correspondence_distance=distance_threshold,
        estimation_method=o3d.pipelines.registration.TransformationEstimationPointToPoint(False),
        ransac_n=4,
        checkers=[
            o3d.pipelines.registration.CorrespondenceCheckerBasedOnEdgeLength(0.9),
            o3d.pipelines.registration.CorrespondenceCheckerBasedOnDistance(distance_threshold)
        ],
        criteria=o3d.pipelines.registration.RANSACConvergenceCriteria(100000, 100)
    )
    return result


def refine_registration(source, target, initial_transformation, voxel_size):
    distance_threshold = voxel_size * 0.4
    source.estimate_normals(o3d.geometry.KDTreeSearchParamHybrid(radius=voxel_size * 2, max_nn=30))
    target.estimate_normals(o3d.geometry.KDTreeSearchParamHybrid(radius=voxel_size * 2, max_nn=30))
    result = o3d.pipelines.registration.registration_icp(
        source, target, distance_threshold, initial_transformation,
        o3d.pipelines.registration.TransformationEstimationPointToPlane()
    )
    return result

def run_icp(path1, path2, bbox_center, bbox_size, bbox_rotation):
    """执行 ICP 配准并返回更新后的 3D 框信息"""
    pcd1 = o3d.io.read_point_cloud(path1)
    pcd2 = o3d.io.read_point_cloud(path2)

    r = R.from_euler('xyz', bbox_rotation).as_matrix()
    obb = o3d.geometry.OrientedBoundingBox(center=bbox_center, R=r, extent=bbox_size)
    pcd1_crop = pcd1.crop(obb)

    threshold = 1.5
    reg_result = o3d.pipelines.registration.registration_icp(
        pcd1_crop, pcd2, threshold, np.eye(4),
        o3d.pipelines.registration.TransformationEstimationPointToPoint()
    )

    T = reg_result.transformation
    rotation_icp = T[:3, :3]
    translation_icp = T[:3, 3]

    new_center = np.dot(rotation_icp, bbox_center) + translation_icp
    new_rotation_matrix = np.dot(rotation_icp, r)
    new_euler = R.from_matrix(new_rotation_matrix).as_euler('xyz')
    # 统计点数
    point_count = len(pcd1_crop.points)

    return {
        "size3D": {
            "x": float(bbox_size[0]),
            "y": float(bbox_size[1]),
            "z": float(bbox_size[2]),
        },
        "center3D": {
            "x": float(new_center[0]),
            "y": float(new_center[1]),
            "z": float(new_center[2]),
        },
        "rotation3D": {
            "x": float(new_euler[0]),
            "y": float(new_euler[1]),
            "z": float(new_euler[2]),
        },
        "points":point_count
    }


def run_icp2(path1, path2, bbox_center, bbox_size, bbox_rotation):
    """执行 ICP 配准并返回更新后的 3D 框信息"""
    pcd1 = o3d.io.read_point_cloud(path1)
    pcd2 = o3d.io.read_point_cloud(path2)

    r = R.from_euler('xyz', bbox_rotation).as_matrix()
    bbox_size_np = np.array(bbox_size)
    # 使用相同大小的裁剪区域以提高配准稳定性
    crop_extent = bbox_size_np * 2.5
    obb = o3d.geometry.OrientedBoundingBox(center=bbox_center, R=r, extent=crop_extent)
    pcd1_crop = pcd1.crop(obb)
    pcd2_crop = pcd2.crop(obb)

    # 检测是否为静止物体
    if is_object_stationary(pcd1_crop, pcd2_crop):
        print(f"检测到静止物体，返回原始位置")
        point_count = len(pcd1_crop.points)
        return {
            "size3D": {
                "x": float(bbox_size[0]),
                "y": float(bbox_size[1]),
                "z": float(bbox_size[2]),
            },
            "center3D": {
                "x": float(bbox_center[0]),
                "y": float(bbox_center[1]),
                "z": float(bbox_center[2]),
            },
            "rotation3D": {
                "x": float(bbox_rotation[0]),
                "y": float(bbox_rotation[1]),
                "z": float(bbox_rotation[2]),
            },
            "points": point_count,
            "is_stationary": True
        }

    # 参数
    voxel_size = 0.2

    # FPFH 粗配准
    source_down, source_fpfh = preprocess_point_cloud(pcd1_crop, voxel_size)
    target_down, target_fpfh = preprocess_point_cloud(pcd2_crop, voxel_size)

    global_result = execute_global_registration(source_down, target_down, source_fpfh, target_fpfh, voxel_size)

    # ICP 微调
    refine_result = refine_registration(source_down, target_down, global_result.transformation, voxel_size)

    T = refine_result.transformation
    rotation_icp = T[:3, :3]
    translation_icp = T[:3, 3]

    new_center = np.dot(rotation_icp, bbox_center) + translation_icp
    new_rotation_matrix = np.dot(rotation_icp, r)
    new_euler = R.from_matrix(new_rotation_matrix).as_euler('xyz')
    # 统计点数
    point_count = len(pcd1_crop.points)

    return {
        "size3D": {
            "x": float(bbox_size[0]),
            "y": float(bbox_size[1]),
            "z": float(bbox_size[2]),
        },
        "center3D": {
            "x": float(new_center[0]),
            "y": float(new_center[1]),
            "z": float(new_center[2]),
        },
        "rotation3D": {
            "x": float(new_euler[0]),
            "y": float(new_euler[1]),
            "z": float(new_euler[2]),
        },
        "points": point_count
    }
def run_icp2_wrapper(args):
    obj,path1, path2 = args
    obj_id = obj.get("objectId")
    size3D = obj.get("size3D", {})
    center3D = obj.get("center3D", {})
    rotation3D = obj.get("rotation3D", {})

    bbox_center = [center3D.get("x"), center3D.get("y"), center3D.get("z")]
    bbox_size = [size3D.get("x"), size3D.get("y"), size3D.get("z")]
    bbox_rotation = [rotation3D.get("x"), rotation3D.get("y"), rotation3D.get("z")]

    start_time = time.time()
    result = run_icp2(path1, path2, bbox_center, bbox_size, bbox_rotation)
    end_time = time.time()

    print(f"objectId={obj_id} run_icp耗时: {end_time - start_time:.2f} 秒")

    result["objectId"] = obj_id
    prev_rotation_matrix = R.from_euler("xyz", bbox_rotation).as_matrix()
    rotation_icp = [result.get("rotation3D").get("x"), result.get("rotation3D").get("y"), result.get("rotation3D").get("z")]
    rotation_icp_matrix = R.from_euler("xyz", rotation_icp).as_matrix()

    angle_diff = rotation_matrix_diff_angle(prev_rotation_matrix, rotation_icp_matrix)
    center3D_icp = [result.get("center3D").get("x"), result.get("center3D").get("y"), result.get("center3D").get("z")]
    offset = np.linalg.norm(np.array(bbox_center) - np.array(center3D_icp) )

    # 根据物体大小调整阈值
    volume = bbox_size[0] * bbox_size[1] * bbox_size[2]
    adaptive_rotation_threshold = max(0.02, min(0.05, volume * 0.001))  # 弧度，约1-3度
    adaptive_position_threshold = max(0.1, min(0.5, volume * 0.01))     # 米

    if angle_diff > adaptive_rotation_threshold:
        print(f"objectId={obj_id} 旋转角度差: {angle_diff:.4f} 弧度 (阈值: {adaptive_rotation_threshold:.4f})")
        result["rotation3D"] = rotation3D
    if offset > adaptive_position_threshold:
        print(f"objectId={obj_id} 偏移距离: {offset:.3f}m (阈值: {adaptive_position_threshold:.3f})")
        result["center3D"] = center3D

    return result
def rotation_matrix_diff_angle(R1, R2):
    """计算两个旋转矩阵之间的夹角（弧度）"""
    R_diff = np.dot(R1.T, R2)
    cos_theta = (np.trace(R_diff) - 1) / 2
    cos_theta = np.clip(cos_theta, -1.0, 1.0)  # 数值稳定
    angle = np.arccos(cos_theta)  # 弧度
    return angle

def is_object_stationary(pcd1_crop, pcd2_crop, threshold=0.1):
    """检测物体是否可能是静止的"""
    if len(pcd1_crop.points) < 10 or len(pcd2_crop.points) < 10:
        return True  # 点太少，假设静止

    # 计算点云重心
    center1 = np.mean(np.asarray(pcd1_crop.points), axis=0)
    center2 = np.mean(np.asarray(pcd2_crop.points), axis=0)

    # 如果重心距离很小，可能是静止的
    distance = np.linalg.norm(center1 - center2)
    return distance < threshold
@app.route('/predict', methods=['POST'])
def predict():
    data = request.get_json()
    # print("predict param 0617:", data)

    # 获取参数
    path1 = data.get('pcd1Url', DEFAULT_PCD1_PATH)
    path2 = data.get('pcd2Url', DEFAULT_PCD2_PATH)
    objects = data.get('objects', [])  # 可选数组参数
    print("predict param :", objects)
    # 创建临时目录
    with tempfile.TemporaryDirectory() as temp_dir:
        try:
            # 记录下载开始时间
            download_start_time = time.time()
            # 下载远程文件到本地临时路径
            path1 = download_file_from_url(path1, temp_dir)

            download_end_time = time.time()

            path2 = download_file_from_url(path2, temp_dir)
            # 记录下载结束时间并计算耗时

            download_duration = download_end_time - download_start_time
            print("下载文件耗时: {:.2f} 秒".format(download_duration))

            print("temp file path :", path1,path2)

            # results = []
            # for obj in objects:
            #     obj_id = obj.get("objectId")
            #     size3D = obj.get("size3D", {})
            #     center3D = obj.get("center3D", {})
            #     rotation3D = obj.get("rotation3D", {})
            #
            #     bbox_center = [center3D.get("x"), center3D.get("y"), center3D.get("z")]
            #     bbox_size = [size3D.get("x"), size3D.get("y"), size3D.get("z")]
            #     bbox_rotation = [rotation3D.get("x"), rotation3D.get("y"), rotation3D.get("z")]
            #     time1 = time.time()
            #     result = run_icp2(path1, path2, bbox_center, bbox_size, bbox_rotation)
            #     time2 = time.time()
            #     print("run_icp耗时: {:.2f} 秒".format(time2 - time1))
            #     result["objectId"] = obj_id
            #     results.append(result)
            arg_list = [(obj, path1, path2) for obj in objects]
            with ThreadPoolExecutor(max_workers=min(8, len(objects))) as executor:
                results = list(executor.map(run_icp2_wrapper, arg_list))
                print("results:", results)
        except Exception as e:
            print(f"An error occurred: {str(e)}")
            traceback.print_exc()
            return jsonify({
                "status": "fail",
                "result": str(e),
                "pcd1": path1,
                "pcd2": path2
            })
        finally:
            # 清理临时文件（TemporaryDirectory 会自动清理）
            pass

    return jsonify({
        "status": "success",
        "result": results,
        "pcd1": path1,
        "pcd2": path2
    })


def download_file_from_url(url, temp_dir):
    """从URL下载文件到临时目录（分块下载、无重试）"""
    headers = {
        'User-Agent': 'Mozilla/5.0 (compatible; FileDownloader/1.0)'
    }

    try:
        with requests.get(url, stream=True, timeout=10, headers=headers) as response:
            response.raise_for_status()  # 自动抛出非200状态异常

            suffix = ".pcd"
            temp_file = tempfile.NamedTemporaryFile(dir=temp_dir, suffix=suffix, delete=False)

            with open(temp_file.name, 'wb') as f:
                shutil.copyfileobj(response.raw, f)

            print("url:", url)
            print("temp file path:", temp_file.name)
            return temp_file.name

    except Exception as e:
        raise RuntimeError(f"下载失败: {e}")


if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5000, debug=True)
