from flask import Flask, request, jsonify
import mysql.connector
import json
import numpy as np
from scipy.spatial.transform import Rotation as R
import open3d as o3d
import tempfile
import requests
import os
import time
import shutil
import traceback
from concurrent.futures import ThreadPoolExecutor
import logging
from datetime import datetime
import uuid

app = Flask(__name__)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('object_tracking.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# 默认点云路径
DEFAULT_PCD1_PATH = "C:/wicv/example5/Scene_01/lidar_point_cloud_0/1744420861.599999.pcd"
DEFAULT_PCD2_PATH = "C:/wicv/example5/Scene_01/lidar_point_cloud_0/1744420861.699999.pcd"

# 全局变量用于跟踪请求
current_request_id = None

# 数据库连接配置
db_config = {
    'host': 'localhost',
    'port': 8191,
    'user': 'xtreme1',
    'password': 'Rc4K3L6f',
    'database': 'xtreme1'
}

def log_with_timestamp(message, level="INFO", obj_id=None):
    """带时间戳的日志输出"""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]
    prefix = f"[{timestamp}]"
    if current_request_id:
        prefix += f"[REQ:{current_request_id[:8]}]"
    if obj_id:
        prefix += f"[OBJ:{obj_id}]"

    full_message = f"{prefix} {message}"

    if level == "ERROR":
        logger.error(full_message)
    elif level == "WARNING":
        logger.warning(full_message)
    else:
        logger.info(full_message)

    # 同时输出到console
    print(full_message)

def log_matrix(matrix, name, obj_id=None):
    """记录矩阵数据"""
    log_with_timestamp(f"{name}:", obj_id=obj_id)
    for i, row in enumerate(matrix):
        row_str = " ".join([f"{val:8.4f}" for val in row])
        log_with_timestamp(f"  [{i}] [{row_str}]", obj_id=obj_id)

def log_vector(vector, name, obj_id=None):
    """记录向量数据"""
    vector_str = " ".join([f"{val:8.4f}" for val in vector])
    log_with_timestamp(f"{name}: [{vector_str}]", obj_id=obj_id)

def log_object_data(obj, stage, obj_id=None):
    """记录物体数据"""
    log_with_timestamp(f"=== {stage} ===", obj_id=obj_id)
    if "center3D" in obj:
        center = obj["center3D"]
        log_vector([center.get("x", 0), center.get("y", 0), center.get("z", 0)], "Center", obj_id)
    if "size3D" in obj:
        size = obj["size3D"]
        log_vector([size.get("x", 0), size.get("y", 0), size.get("z", 0)], "Size", obj_id)
    if "rotation3D" in obj:
        rotation = obj["rotation3D"]
        log_vector([rotation.get("x", 0), rotation.get("y", 0), rotation.get("z", 0)], "Rotation", obj_id)
    if "points" in obj:
        log_with_timestamp(f"Points: {obj['points']}", obj_id=obj_id)


def get_contour_info_by_id(object_id):
    """从数据库中获取 contour 信息"""
    try:
        conn = mysql.connector.connect(**db_config)
        cursor = conn.cursor()

        query = "SELECT class_attributes FROM data_annotation_object WHERE id = %s"
        cursor.execute(query, (object_id,))
        result = cursor.fetchone()

        if not result:
            return None

        class_attributes = json.loads(result[0])
        contour_info = class_attributes.get("contour", None)

        if not contour_info:
            return {"error": f"No 'contour' field for id={object_id}"}

        return contour_info

    except mysql.connector.Error as err:
        return {"error": f"Database error: {err}"}
    except json.JSONDecodeError as jde:
        return {"error": f"JSON decode error: {jde}"}
    finally:
        if 'conn' in locals() and conn.is_connected():
            cursor.close()
            conn.close()


def preprocess_point_cloud(pcd, voxel_size):
    # 下采样
    pcd_down = pcd.voxel_down_sample(voxel_size)

    # 法线估计
    pcd_down.estimate_normals(o3d.geometry.KDTreeSearchParamHybrid(radius=voxel_size * 2, max_nn=30))

    # FPFH 特征
    fpfh = o3d.pipelines.registration.compute_fpfh_feature(
        pcd_down,
        o3d.geometry.KDTreeSearchParamHybrid(radius=voxel_size * 5, max_nn=100)
    )
    return pcd_down, fpfh


def execute_global_registration(source_down, target_down, source_fpfh, target_fpfh, voxel_size):
    distance_threshold = voxel_size * 1.5
    result = o3d.pipelines.registration.registration_ransac_based_on_feature_matching(
        source_down, target_down, source_fpfh, target_fpfh,
        mutual_filter=True,
        max_correspondence_distance=distance_threshold,
        estimation_method=o3d.pipelines.registration.TransformationEstimationPointToPoint(False),
        ransac_n=4,
        checkers=[
            o3d.pipelines.registration.CorrespondenceCheckerBasedOnEdgeLength(0.9),
            o3d.pipelines.registration.CorrespondenceCheckerBasedOnDistance(distance_threshold)
        ],
        criteria=o3d.pipelines.registration.RANSACConvergenceCriteria(100000, 100)
    )
    return result


def refine_registration(source, target, initial_transformation, voxel_size):
    distance_threshold = voxel_size * 0.4
    source.estimate_normals(o3d.geometry.KDTreeSearchParamHybrid(radius=voxel_size * 2, max_nn=30))
    target.estimate_normals(o3d.geometry.KDTreeSearchParamHybrid(radius=voxel_size * 2, max_nn=30))
    result = o3d.pipelines.registration.registration_icp(
        source, target, distance_threshold, initial_transformation,
        o3d.pipelines.registration.TransformationEstimationPointToPlane()
    )
    return result

def run_icp(path1, path2, bbox_center, bbox_size, bbox_rotation):
    """执行 ICP 配准并返回更新后的 3D 框信息"""
    pcd1 = o3d.io.read_point_cloud(path1)
    pcd2 = o3d.io.read_point_cloud(path2)

    r = R.from_euler('xyz', bbox_rotation).as_matrix()
    obb = o3d.geometry.OrientedBoundingBox(center=bbox_center, R=r, extent=bbox_size)
    pcd1_crop = pcd1.crop(obb)

    threshold = 1.5
    reg_result = o3d.pipelines.registration.registration_icp(
        pcd1_crop, pcd2, threshold, np.eye(4),
        o3d.pipelines.registration.TransformationEstimationPointToPoint()
    )

    T = reg_result.transformation
    rotation_icp = T[:3, :3]
    translation_icp = T[:3, 3]

    new_center = np.dot(rotation_icp, bbox_center) + translation_icp
    new_rotation_matrix = np.dot(rotation_icp, r)
    new_euler = R.from_matrix(new_rotation_matrix).as_euler('xyz')
    # 统计点数
    point_count = len(pcd1_crop.points)

    return {
        "size3D": {
            "x": float(bbox_size[0]),
            "y": float(bbox_size[1]),
            "z": float(bbox_size[2]),
        },
        "center3D": {
            "x": float(new_center[0]),
            "y": float(new_center[1]),
            "z": float(new_center[2]),
        },
        "rotation3D": {
            "x": float(new_euler[0]),
            "y": float(new_euler[1]),
            "z": float(new_euler[2]),
        },
        "points":point_count
    }


def run_icp2(path1, path2, bbox_center, bbox_size, bbox_rotation, ego_transform=None, obj_id="unknown"):
    """执行物体跟踪预测并返回更新后的 3D 框信息

    核心思路：
    1. 对于静止物体：直接应用自车运动的逆变换
    2. 对于运动物体：ICP配准结果 = 自车运动 + 物体运动，需要分离

    Args:
        ego_transform: 4x4变换矩阵，表示自车从frame1到frame2的运动
                      如果为None，则通过点云配准估计全局变换
        obj_id: 物体ID，用于日志标识
    """
    log_with_timestamp(f"开始处理物体跟踪", obj_id=obj_id)
    log_with_timestamp(f"点云文件1: {path1}", obj_id=obj_id)
    log_with_timestamp(f"点云文件2: {path2}", obj_id=obj_id)
    log_vector(bbox_center, "输入中心位置", obj_id)
    log_vector(bbox_size, "输入框大小", obj_id)
    log_vector(bbox_rotation, "输入旋转角度", obj_id)

    # 记录开始时间
    start_time = time.time()

    try:
        pcd1 = o3d.io.read_point_cloud(path1)
        pcd2 = o3d.io.read_point_cloud(path2)
        log_with_timestamp(f"点云加载成功 - PCD1: {len(pcd1.points)}点, PCD2: {len(pcd2.points)}点", obj_id=obj_id)
    except Exception as e:
        log_with_timestamp(f"点云加载失败: {e}", "ERROR", obj_id)
        raise

    r = R.from_euler('xyz', bbox_rotation).as_matrix()
    bbox_size_np = np.array(bbox_size)
    # 使用相同大小的裁剪区域以提高配准稳定性
    crop_extent = bbox_size_np * 2.5
    log_vector(crop_extent, "裁剪区域大小", obj_id)

    obb = o3d.geometry.OrientedBoundingBox(center=bbox_center, R=r, extent=crop_extent)
    pcd1_crop = pcd1.crop(obb)
    pcd2_crop = pcd2.crop(obb)

    log_with_timestamp(f"点云裁剪完成 - PCD1裁剪后: {len(pcd1_crop.points)}点, PCD2裁剪后: {len(pcd2_crop.points)}点", obj_id=obj_id)

    # 如果没有自车变换信息，先估计全局变换
    if ego_transform is None:
        log_with_timestamp("未提供自车变换，开始估计全局变换", obj_id=obj_id)
        ego_transform = estimate_global_transform(pcd1, pcd2)
        log_matrix(ego_transform, "估计的自车变换矩阵", obj_id)
    else:
        log_matrix(ego_transform, "提供的自车变换矩阵", obj_id)

    # 暂时强制所有物体都按静止物体处理，用于调试
    log_with_timestamp("=== 强制按静止物体处理（调试模式）===", obj_id=obj_id)
    stationary_result = True
    log_with_timestamp(f"静止物体检测结果: {stationary_result} (强制设置)", obj_id=obj_id)

    if stationary_result:
        log_with_timestamp("*** 检测到静止物体，应用自车运动补偿 ***", obj_id=obj_id)
        point_count = len(pcd1_crop.points)

        # 对于静止物体，应用自车运动的逆变换
        log_with_timestamp("开始计算自车运动补偿", obj_id=obj_id)

        # 计算逆变换矩阵
        ego_transform_inv = np.linalg.inv(ego_transform)
        log_matrix(ego_transform_inv, "自车逆变换矩阵", obj_id)

        # 应用逆变换到物体位置
        bbox_center_homo = np.append(bbox_center, 1.0)
        log_vector(bbox_center_homo, "原始中心齐次坐标", obj_id)

        new_center_homo = np.dot(ego_transform_inv, bbox_center_homo)
        log_vector(new_center_homo, "变换后齐次坐标", obj_id)

        new_center = new_center_homo[:3]

        log_vector(bbox_center, "原始中心", obj_id)
        log_vector(new_center, "变换后中心", obj_id)

        position_change = new_center - np.array(bbox_center)
        log_vector(position_change, "位置变化", obj_id)
        log_with_timestamp(f"位置变化距离: {np.linalg.norm(position_change):.4f}m", obj_id=obj_id)

        # 旋转变换
        log_with_timestamp("开始计算旋转变换", obj_id=obj_id)
        ego_rotation = ego_transform_inv[:3, :3]
        log_matrix(ego_rotation, "自车旋转逆变换", obj_id)

        original_rotation_matrix = R.from_euler('xyz', bbox_rotation).as_matrix()
        log_matrix(original_rotation_matrix, "原始旋转矩阵", obj_id)

        new_rotation_matrix = np.dot(ego_rotation, original_rotation_matrix)
        log_matrix(new_rotation_matrix, "变换后旋转矩阵", obj_id)

        new_euler = R.from_matrix(new_rotation_matrix).as_euler('xyz')

        log_vector(bbox_rotation, "原始旋转", obj_id)
        log_vector(new_euler, "变换后旋转", obj_id)

        rotation_change = new_euler - np.array(bbox_rotation)
        log_vector(rotation_change, "旋转变化", obj_id)

        # 验证变换的合理性
        log_with_timestamp("验证变换合理性", obj_id=obj_id)
        if np.linalg.norm(position_change) > 10.0:
            log_with_timestamp(f"警告：位置变化过大 {np.linalg.norm(position_change):.2f}m", "WARNING", obj_id)
        if np.linalg.norm(rotation_change) > 1.0:
            log_with_timestamp(f"警告：旋转变化过大 {np.linalg.norm(rotation_change):.2f}rad", "WARNING", obj_id)

        # 构建最终结果（静止物体直接使用变换后的位置，无阈值检查）
        result = {
            "size3D": {
                "x": float(bbox_size[0]),
                "y": float(bbox_size[1]),
                "z": float(bbox_size[2]),
            },
            "center3D": {
                "x": float(new_center[0]),
                "y": float(new_center[1]),
                "z": float(new_center[2]),
            },
            "rotation3D": {
                "x": float(new_euler[0]),
                "y": float(new_euler[1]),
                "z": float(new_euler[2]),
            },
            "points": point_count,
            "is_stationary": True,
            "ego_compensated": True
        }

        # 记录处理时间
        processing_time = time.time() - start_time
        log_with_timestamp(f"静止物体处理完成，耗时: {processing_time:.3f}秒", obj_id=obj_id)
        log_object_data(result, "静止物体最终输出结果", obj_id)

        # 最终验证
        final_position_change = np.array([result["center3D"]["x"], result["center3D"]["y"], result["center3D"]["z"]]) - np.array(bbox_center)
        log_vector(final_position_change, "最终位置变化", obj_id)
        log_with_timestamp(f"最终位置变化距离: {np.linalg.norm(final_position_change):.4f}m", obj_id=obj_id)

        return result

    # 暂时注释掉运动物体处理，专注于静止物体调试
    log_with_timestamp("*** 运动物体处理已暂时禁用，返回原始位置 ***", obj_id=obj_id)

    # 对于运动物体，暂时返回原始位置
    result = {
        "size3D": {
            "x": float(bbox_size[0]),
            "y": float(bbox_size[1]),
            "z": float(bbox_size[2]),
        },
        "center3D": {
            "x": float(bbox_center[0]),
            "y": float(bbox_center[1]),
            "z": float(bbox_center[2]),
        },
        "rotation3D": {
            "x": float(bbox_rotation[0]),
            "y": float(bbox_rotation[1]),
            "z": float(bbox_rotation[2]),
        },
        "points": len(pcd1_crop.points),
        "is_stationary": False,
        "debug_mode": "moving_object_disabled"
    }

    processing_time = time.time() - start_time
    log_with_timestamp(f"运动物体处理跳过，耗时: {processing_time:.3f}秒", obj_id=obj_id)
    return result
def run_icp2_wrapper(args):
    obj, path1, path2, ego_transform = args
    obj_id = obj.get("objectId", "unknown")
    size3D = obj.get("size3D", {})
    center3D = obj.get("center3D", {})
    rotation3D = obj.get("rotation3D", {})

    bbox_center = [center3D.get("x", 0), center3D.get("y", 0), center3D.get("z", 0)]
    bbox_size = [size3D.get("x", 1), size3D.get("y", 1), size3D.get("z", 1)]
    bbox_rotation = [rotation3D.get("x", 0), rotation3D.get("y", 0), rotation3D.get("z", 0)]

    log_with_timestamp("=== 开始处理物体 ===", obj_id=obj_id)
    log_object_data({
        "center3D": center3D,
        "size3D": size3D,
        "rotation3D": rotation3D
    }, "输入数据", obj_id)

    wrapper_start_time = time.time()

    try:
        result = run_icp2(path1, path2, bbox_center, bbox_size, bbox_rotation, ego_transform, obj_id)
        wrapper_end_time = time.time()

        log_with_timestamp(f"物体处理总耗时: {wrapper_end_time - wrapper_start_time:.3f}秒", obj_id=obj_id)

    except Exception as e:
        log_with_timestamp(f"物体处理失败: {e}", "ERROR", obj_id)
        log_with_timestamp(f"错误详情: {traceback.format_exc()}", "ERROR", obj_id)
        # 返回原始位置作为fallback
        result = {
            "size3D": size3D,
            "center3D": center3D,
            "rotation3D": rotation3D,
            "points": 0,
            "error": str(e)
        }

    result["objectId"] = obj_id

    # 分析预测结果
    if "error" not in result:
        predicted_center = [result.get("center3D").get("x"), result.get("center3D").get("y"), result.get("center3D").get("z")]
        predicted_rotation = [result.get("rotation3D").get("x"), result.get("rotation3D").get("y"), result.get("rotation3D").get("z")]

        log_vector(predicted_center, "预测框位置", obj_id)
        log_vector(predicted_rotation, "预测框旋转", obj_id)

        # 计算位置和旋转变化
        position_change = np.array(predicted_center) - np.array(bbox_center)
        rotation_change = np.array(predicted_rotation) - np.array(bbox_rotation)

        log_vector(position_change, "位置变化", obj_id)
        log_vector(rotation_change, "旋转变化", obj_id)
        log_with_timestamp(f"位置变化距离: {np.linalg.norm(position_change):.4f}m", obj_id=obj_id)
        log_with_timestamp(f"旋转变化幅度: {np.linalg.norm(rotation_change):.4f}rad", obj_id=obj_id)

        # 检查是否为静止物体
        is_stationary = result.get("is_stationary", False)
        log_with_timestamp(f"物体类型: {'静止物体' if is_stationary else '运动物体'}", obj_id=obj_id)

        # 计算阈值检查
        prev_rotation_matrix = R.from_euler("xyz", bbox_rotation).as_matrix()
        rotation_icp_matrix = R.from_euler("xyz", predicted_rotation).as_matrix()
        angle_diff = rotation_matrix_diff_angle(prev_rotation_matrix, rotation_icp_matrix)
        offset = np.linalg.norm(position_change)

        log_with_timestamp(f"旋转角度差: {angle_diff:.4f}弧度", obj_id=obj_id)
        log_with_timestamp(f"位置偏移: {offset:.4f}m", obj_id=obj_id)

        # 根据物体大小和类型调整阈值
        volume = bbox_size[0] * bbox_size[1] * bbox_size[2]
        is_stationary = result.get("is_stationary", False)

        if is_stationary:
            # 静止物体：阈值相对严格，因为变化应该主要来自自车运动
            adaptive_rotation_threshold = max(0.05, min(0.1, volume * 0.002))   # 弧度，约3-6度
            adaptive_position_threshold = max(0.2, min(1.0, volume * 0.02))     # 米
        else:
            # 运动物体：阈值相对宽松，允许更大的运动
            adaptive_rotation_threshold = max(0.1, min(0.3, volume * 0.005))    # 弧度，约6-17度
            adaptive_position_threshold = max(0.5, min(2.0, volume * 0.05))     # 米

        log_with_timestamp(f"自适应阈值 - 旋转: {adaptive_rotation_threshold:.4f}弧度, 位置: {adaptive_position_threshold:.3f}m", obj_id=obj_id)
        log_with_timestamp(f"物体类型: {'静止物体' if is_stationary else '运动物体'} - 使用{'严格' if is_stationary else '宽松'}阈值", obj_id=obj_id)

        # 对于运动物体，还要考虑ICP配准质量
        skip_threshold_check = False
        if not is_stationary:
            # 检查ICP配准质量，如果质量很好，可以放宽阈值
            # 这里可以根据之前记录的fitness和RMSE来判断
            # 暂时简化：如果是运动物体且变化不是特别极端，就信任ICP结果
            if offset < 5.0 and angle_diff < 1.0:  # 位置变化<5米，旋转<57度
                log_with_timestamp(f"运动物体变化在合理范围内，跳过严格阈值检查", obj_id=obj_id)
                skip_threshold_check = True

        # 阈值检查和修正
        corrections_made = []
        if not skip_threshold_check:
            if angle_diff > adaptive_rotation_threshold:
                log_with_timestamp(f"*** 旋转变化过大，恢复原始旋转 ***", "WARNING", obj_id)
                log_with_timestamp(f"旋转角度差: {angle_diff:.4f} > 阈值: {adaptive_rotation_threshold:.4f}", obj_id=obj_id)
                result["rotation3D"] = rotation3D
                corrections_made.append("旋转")

            if offset > adaptive_position_threshold:
                log_with_timestamp(f"*** 位置变化过大，恢复原始位置 ***", "WARNING", obj_id)
                log_with_timestamp(f"偏移距离: {offset:.4f} > 阈值: {adaptive_position_threshold:.4f}", obj_id=obj_id)
                result["center3D"] = center3D
                corrections_made.append("位置")
        else:
            log_with_timestamp(f"跳过阈值检查 - 旋转: {angle_diff:.4f}弧度, 位置: {offset:.4f}m", obj_id=obj_id)

        if corrections_made:
            log_with_timestamp(f"应用了阈值修正: {', '.join(corrections_made)}", obj_id=obj_id)
        else:
            log_with_timestamp("未触发阈值修正", obj_id=obj_id)

        # 打印最终结果
        final_center = [result.get("center3D").get("x"), result.get("center3D").get("y"), result.get("center3D").get("z")]
        final_rotation = [result.get("rotation3D").get("x"), result.get("rotation3D").get("y"), result.get("rotation3D").get("z")]
        final_position_change = np.array(final_center) - np.array(bbox_center)
        final_rotation_change = np.array(final_rotation) - np.array(bbox_rotation)

        log_vector(final_center, "最终框位置", obj_id)
        log_vector(final_rotation, "最终框旋转", obj_id)
        log_vector(final_position_change, "最终位置变化", obj_id)
        log_vector(final_rotation_change, "最终旋转变化", obj_id)
        log_with_timestamp(f"最终位置变化距离: {np.linalg.norm(final_position_change):.4f}m", obj_id=obj_id)

    log_with_timestamp("=== 物体处理完成 ===", obj_id=obj_id)
    return result


def rotation_matrix_diff_angle(R1, R2):
    """Calculate the angle between two rotation matrices (in radians)"""
    R_diff = np.dot(R1.T, R2)
    cos_theta = (np.trace(R_diff) - 1) / 2
    cos_theta = np.clip(cos_theta, -1.0, 1.0)  # numerical stability
    angle = np.arccos(cos_theta)  # radians
    return angle

def is_object_stationary(pcd1_crop, pcd2_crop, threshold=0.1, obj_id=None):
    """检测物体是否可能是静止的"""
    points1_count = len(pcd1_crop.points)
    points2_count = len(pcd2_crop.points)

    log_with_timestamp(f"静止检测 - 点云1点数: {points1_count}, 点云2点数: {points2_count}", obj_id=obj_id)

    if points1_count < 10 or points2_count < 10:
        log_with_timestamp(f"点数太少，假设为静止物体", obj_id=obj_id)
        return True  # 点太少，假设静止

    try:
        # 计算点云重心
        center1 = np.mean(np.asarray(pcd1_crop.points), axis=0)
        center2 = np.mean(np.asarray(pcd2_crop.points), axis=0)

        log_vector(center1, "点云1重心", obj_id)
        log_vector(center2, "点云2重心", obj_id)

        # 如果重心距离很小，可能是静止的
        distance = np.linalg.norm(center1 - center2)
        log_with_timestamp(f"重心距离: {distance:.4f}m, 阈值: {threshold:.4f}m", obj_id=obj_id)

        is_stationary = distance < threshold
        log_with_timestamp(f"静止判断结果: {is_stationary}", obj_id=obj_id)

        return is_stationary

    except Exception as e:
        log_with_timestamp(f"静止检测失败: {e}", "ERROR", obj_id)
        return True  # 出错时假设静止

def estimate_global_transform(pcd1, pcd2, voxel_size=0.5):
    """估计两个点云之间的全局变换（自车运动）"""
    try:
        # 下采样以提高速度
        pcd1_down = pcd1.voxel_down_sample(voxel_size)
        pcd2_down = pcd2.voxel_down_sample(voxel_size)

        # 估计法线
        pcd1_down.estimate_normals(o3d.geometry.KDTreeSearchParamHybrid(radius=voxel_size * 2, max_nn=30))
        pcd2_down.estimate_normals(o3d.geometry.KDTreeSearchParamHybrid(radius=voxel_size * 2, max_nn=30))

        # 计算FPFH特征
        fpfh1 = o3d.pipelines.registration.compute_fpfh_feature(
            pcd1_down, o3d.geometry.KDTreeSearchParamHybrid(radius=voxel_size * 5, max_nn=100))
        fpfh2 = o3d.pipelines.registration.compute_fpfh_feature(
            pcd2_down, o3d.geometry.KDTreeSearchParamHybrid(radius=voxel_size * 5, max_nn=100))

        # 全局配准
        result = execute_global_registration(pcd1_down, pcd2_down, fpfh1, fpfh2, voxel_size)

        # ICP精配准
        refined_result = refine_registration(pcd1_down, pcd2_down, result.transformation, voxel_size)

        return refined_result.transformation
    except Exception as e:
        print(f"全局变换估计失败: {e}")
        return np.eye(4)  # 返回单位矩阵作为fallback

def parse_ego_pose(ego_pose_data):
    """解析自车位姿数据并构建4x4变换矩阵

    Args:
        ego_pose_data: 包含自车位姿信息的字典，可能的格式：
                      - {"translation": [x, y, z], "rotation": [rx, ry, rz]}
                      - {"transform_matrix": [[...], [...], [...], [...]]}
                      - {"pose1": {...}, "pose2": {...}}

    Returns:
        4x4 numpy array representing the transformation matrix
    """
    try:
        if "transform_matrix" in ego_pose_data:
            # 直接提供变换矩阵
            return np.array(ego_pose_data["transform_matrix"])

        elif "translation" in ego_pose_data and "rotation" in ego_pose_data:
            # 提供平移和旋转
            translation = np.array(ego_pose_data["translation"])
            rotation = np.array(ego_pose_data["rotation"])

            # 构建变换矩阵
            rotation_matrix = R.from_euler('xyz', rotation).as_matrix()
            transform = np.eye(4)
            transform[:3, :3] = rotation_matrix
            transform[:3, 3] = translation
            return transform

        elif "pose1" in ego_pose_data and "pose2" in ego_pose_data:
            # 提供两个位姿，计算相对变换
            pose1 = ego_pose_data["pose1"]
            pose2 = ego_pose_data["pose2"]

            # 构建两个位姿的变换矩阵
            T1 = build_transform_matrix(pose1)
            T2 = build_transform_matrix(pose2)

            # 计算相对变换 T1 -> T2
            return np.dot(T2, np.linalg.inv(T1))

        else:
            print("未识别的自车位姿数据格式")
            return None

    except Exception as e:
        print(f"解析自车位姿失败: {e}")
        return None

def build_transform_matrix(pose):
    """从位姿字典构建4x4变换矩阵"""
    translation = np.array([pose.get("x", 0), pose.get("y", 0), pose.get("z", 0)])
    rotation = np.array([pose.get("rx", 0), pose.get("ry", 0), pose.get("rz", 0)])

    rotation_matrix = R.from_euler('xyz', rotation).as_matrix()
    transform = np.eye(4)
    transform[:3, :3] = rotation_matrix
    transform[:3, 3] = translation
    return transform
@app.route('/predict', methods=['POST'])
def predict():
    global current_request_id
    current_request_id = str(uuid.uuid4())

    log_with_timestamp("=== 新的预测请求开始 ===")

    try:
        data = request.get_json()
        log_with_timestamp(f"接收到请求数据: {json.dumps(data, indent=2)}")

        # 获取参数
        path1 = data.get('pcd1Url', DEFAULT_PCD1_PATH)
        path2 = data.get('pcd2Url', DEFAULT_PCD2_PATH)
        objects = data.get('objects', [])  # 可选数组参数

        log_with_timestamp(f"点云文件1: {path1}")
        log_with_timestamp(f"点云文件2: {path2}")
        log_with_timestamp(f"物体数量: {len(objects)}")

        # 获取自车位姿信息（可选）
        ego_pose_data = data.get('egoPose', None)
        ego_transform = None
        if ego_pose_data:
            log_with_timestamp(f"自车位姿数据: {json.dumps(ego_pose_data, indent=2)}")
            # 解析自车位姿信息并构建变换矩阵
            ego_transform = parse_ego_pose(ego_pose_data)
            if ego_transform is not None:
                log_matrix(ego_transform, "解析的自车变换矩阵")
            else:
                log_with_timestamp("自车位姿解析失败", "ERROR")

        log_with_timestamp(f"自车位姿信息: {'已提供' if ego_transform is not None else '未提供'}")

        log_with_timestamp("处理的物体列表:")
        for i, obj in enumerate(objects):
            obj_id = obj.get("objectId", f"obj_{i}")
            center = obj.get("center3D", {})
            log_with_timestamp(f"  {i+1}. objectId: {obj_id}, center: [{center.get('x', 0):.3f}, {center.get('y', 0):.3f}, {center.get('z', 0):.3f}]")

    except Exception as e:
        log_with_timestamp(f"请求数据解析失败: {e}", "ERROR")
        return jsonify({
            "status": "fail",
            "result": f"请求数据解析失败: {e}",
            "request_id": current_request_id
        })
    # 创建临时目录
    with tempfile.TemporaryDirectory() as temp_dir:
        try:
            # 记录下载开始时间
            download_start_time = time.time()
            log_with_timestamp("开始下载点云文件")

            # 下载远程文件到本地临时路径
            path1 = download_file_from_url(path1, temp_dir)
            download_mid_time = time.time()

            path2 = download_file_from_url(path2, temp_dir)
            download_end_time = time.time()

            download_duration = download_end_time - download_start_time
            log_with_timestamp(f"文件下载完成，总耗时: {download_duration:.3f}秒")
            log_with_timestamp(f"文件1下载耗时: {download_mid_time - download_start_time:.3f}秒")
            log_with_timestamp(f"文件2下载耗时: {download_end_time - download_mid_time:.3f}秒")
            log_with_timestamp(f"临时文件路径1: {path1}")
            log_with_timestamp(f"临时文件路径2: {path2}")

            # results = []
            # for obj in objects:
            #     obj_id = obj.get("objectId")
            #     size3D = obj.get("size3D", {})
            #     center3D = obj.get("center3D", {})
            #     rotation3D = obj.get("rotation3D", {})
            #
            #     bbox_center = [center3D.get("x"), center3D.get("y"), center3D.get("z")]
            #     bbox_size = [size3D.get("x"), size3D.get("y"), size3D.get("z")]
            #     bbox_rotation = [rotation3D.get("x"), rotation3D.get("y"), rotation3D.get("z")]
            #     time1 = time.time()
            #     result = run_icp2(path1, path2, bbox_center, bbox_size, bbox_rotation)
            #     time2 = time.time()
            #     print("run_icp耗时: {:.2f} 秒".format(time2 - time1))
            #     result["objectId"] = obj_id
            #     results.append(result)
            # 开始并行处理物体
            processing_start_time = time.time()
            log_with_timestamp(f"开始并行处理 {len(objects)} 个物体")

            arg_list = [(obj, path1, path2, ego_transform) for obj in objects]
            max_workers = min(8, len(objects)) if objects else 1

            with ThreadPoolExecutor(max_workers=max_workers) as executor:
                results = list(executor.map(run_icp2_wrapper, arg_list))

            processing_end_time = time.time()
            processing_duration = processing_end_time - processing_start_time
            log_with_timestamp(f"所有物体处理完成，耗时: {processing_duration:.3f}秒")

            # 统计结果
            stationary_count = sum(1 for r in results if r.get("is_stationary", False))
            moving_count = len(results) - stationary_count
            error_count = sum(1 for r in results if "error" in r)

            log_with_timestamp(f"处理结果统计 - 静止物体: {stationary_count}, 运动物体: {moving_count}, 错误: {error_count}")

            # 记录每个物体的最终结果
            for i, result in enumerate(results):
                obj_id = result.get("objectId", f"obj_{i}")
                if "error" not in result:
                    center = result.get("center3D", {})
                    log_with_timestamp(f"物体 {obj_id} 最终位置: [{center.get('x', 0):.4f}, {center.get('y', 0):.4f}, {center.get('z', 0):.4f}]")
                else:
                    log_with_timestamp(f"物体 {obj_id} 处理失败: {result.get('error')}", "ERROR")
        except Exception as e:
            error_msg = f"处理过程中发生错误: {str(e)}"
            log_with_timestamp(error_msg, "ERROR")
            log_with_timestamp(f"错误详情: {traceback.format_exc()}", "ERROR")

            return jsonify({
                "status": "fail",
                "result": error_msg,
                "request_id": current_request_id,
                "pcd1": path1,
                "pcd2": path2,
                "timestamp": datetime.now().isoformat()
            })
        finally:
            # 清理临时文件（TemporaryDirectory 会自动清理）
            log_with_timestamp("临时文件清理完成")

    # 计算总耗时
    total_end_time = time.time()
    total_duration = total_end_time - download_start_time

    log_with_timestamp(f"=== 预测请求完成，总耗时: {total_duration:.3f}秒 ===")

    return jsonify({
        "status": "success",
        "result": results,
        "request_id": current_request_id,
        "pcd1": path1,
        "pcd2": path2,
        "processing_time": {
            "download": download_duration,
            "processing": processing_duration,
            "total": total_duration
        },
        "statistics": {
            "total_objects": len(objects),
            "stationary_objects": stationary_count,
            "moving_objects": moving_count,
            "error_objects": error_count
        },
        "timestamp": datetime.now().isoformat()
    })


def download_file_from_url(url, temp_dir):
    """从URL下载文件到临时目录（分块下载、无重试）"""
    log_with_timestamp(f"开始下载文件: {url}")

    headers = {
        'User-Agent': 'Mozilla/5.0 (compatible; FileDownloader/1.0)'
    }

    try:
        download_start = time.time()
        with requests.get(url, stream=True, timeout=30, headers=headers) as response:
            response.raise_for_status()  # 自动抛出非200状态异常

            content_length = response.headers.get('content-length')
            if content_length:
                log_with_timestamp(f"文件大小: {int(content_length) / 1024 / 1024:.2f} MB")

            suffix = ".pcd"
            temp_file = tempfile.NamedTemporaryFile(dir=temp_dir, suffix=suffix, delete=False)

            with open(temp_file.name, 'wb') as f:
                shutil.copyfileobj(response.raw, f)

            download_end = time.time()
            download_time = download_end - download_start
            file_size = os.path.getsize(temp_file.name) / 1024 / 1024  # MB

            log_with_timestamp(f"文件下载完成: {temp_file.name}")
            log_with_timestamp(f"下载耗时: {download_time:.3f}秒, 文件大小: {file_size:.2f}MB")
            log_with_timestamp(f"下载速度: {file_size/download_time:.2f}MB/s")

            return temp_file.name

    except Exception as e:
        error_msg = f"下载失败 {url}: {e}"
        log_with_timestamp(error_msg, "ERROR")
        raise RuntimeError(error_msg)


if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5000, debug=True)
