from flask import Flask, request, jsonify
import mysql.connector
import json
import numpy as np
from scipy.spatial.transform import Rotation as R
import open3d as o3d
import tempfile
import requests
import os
import time
import shutil
import traceback
from concurrent.futures import ThreadPoolExecutor

app = Flask(__name__)

# 默认点云路径
DEFAULT_PCD1_PATH = "C:/wicv/example5/Scene_01/lidar_point_cloud_0/1744420861.599999.pcd"
DEFAULT_PCD2_PATH = "C:/wicv/example5/Scene_01/lidar_point_cloud_0/1744420861.699999.pcd"

# 数据库连接配置
db_config = {
    'host': 'localhost',
    'port': 8191,
    'user': 'xtreme1',
    'password': 'Rc4K3L6f',
    'database': 'xtreme1'
}


def get_contour_info_by_id(object_id):
    """从数据库中获取 contour 信息"""
    try:
        conn = mysql.connector.connect(**db_config)
        cursor = conn.cursor()

        query = "SELECT class_attributes FROM data_annotation_object WHERE id = %s"
        cursor.execute(query, (object_id,))
        result = cursor.fetchone()

        if not result:
            return None

        class_attributes = json.loads(result[0])
        contour_info = class_attributes.get("contour", None)

        if not contour_info:
            return {"error": f"No 'contour' field for id={object_id}"}

        return contour_info

    except mysql.connector.Error as err:
        return {"error": f"Database error: {err}"}
    except json.JSONDecodeError as jde:
        return {"error": f"JSON decode error: {jde}"}
    finally:
        if 'conn' in locals() and conn.is_connected():
            cursor.close()
            conn.close()


def preprocess_point_cloud(pcd, voxel_size):
    # 下采样
    pcd_down = pcd.voxel_down_sample(voxel_size)

    # 法线估计
    pcd_down.estimate_normals(o3d.geometry.KDTreeSearchParamHybrid(radius=voxel_size * 2, max_nn=30))

    # FPFH 特征
    fpfh = o3d.pipelines.registration.compute_fpfh_feature(
        pcd_down,
        o3d.geometry.KDTreeSearchParamHybrid(radius=voxel_size * 5, max_nn=100)
    )
    return pcd_down, fpfh


def execute_global_registration(source_down, target_down, source_fpfh, target_fpfh, voxel_size):
    distance_threshold = voxel_size * 1.5
    result = o3d.pipelines.registration.registration_ransac_based_on_feature_matching(
        source_down, target_down, source_fpfh, target_fpfh,
        mutual_filter=True,
        max_correspondence_distance=distance_threshold,
        estimation_method=o3d.pipelines.registration.TransformationEstimationPointToPoint(False),
        ransac_n=4,
        checkers=[
            o3d.pipelines.registration.CorrespondenceCheckerBasedOnEdgeLength(0.9),
            o3d.pipelines.registration.CorrespondenceCheckerBasedOnDistance(distance_threshold)
        ],
        criteria=o3d.pipelines.registration.RANSACConvergenceCriteria(100000, 100)
    )
    return result


def refine_registration(source, target, initial_transformation, voxel_size):
    distance_threshold = voxel_size * 0.4
    source.estimate_normals(o3d.geometry.KDTreeSearchParamHybrid(radius=voxel_size * 2, max_nn=30))
    target.estimate_normals(o3d.geometry.KDTreeSearchParamHybrid(radius=voxel_size * 2, max_nn=30))
    result = o3d.pipelines.registration.registration_icp(
        source, target, distance_threshold, initial_transformation,
        o3d.pipelines.registration.TransformationEstimationPointToPlane()
    )
    return result

def run_icp(path1, path2, bbox_center, bbox_size, bbox_rotation):
    """执行 ICP 配准并返回更新后的 3D 框信息"""
    pcd1 = o3d.io.read_point_cloud(path1)
    pcd2 = o3d.io.read_point_cloud(path2)

    r = R.from_euler('xyz', bbox_rotation).as_matrix()
    obb = o3d.geometry.OrientedBoundingBox(center=bbox_center, R=r, extent=bbox_size)
    pcd1_crop = pcd1.crop(obb)

    threshold = 1.5
    reg_result = o3d.pipelines.registration.registration_icp(
        pcd1_crop, pcd2, threshold, np.eye(4),
        o3d.pipelines.registration.TransformationEstimationPointToPoint()
    )

    T = reg_result.transformation
    rotation_icp = T[:3, :3]
    translation_icp = T[:3, 3]

    new_center = np.dot(rotation_icp, bbox_center) + translation_icp
    new_rotation_matrix = np.dot(rotation_icp, r)
    new_euler = R.from_matrix(new_rotation_matrix).as_euler('xyz')
    # 统计点数
    point_count = len(pcd1_crop.points)

    return {
        "size3D": {
            "x": float(bbox_size[0]),
            "y": float(bbox_size[1]),
            "z": float(bbox_size[2]),
        },
        "center3D": {
            "x": float(new_center[0]),
            "y": float(new_center[1]),
            "z": float(new_center[2]),
        },
        "rotation3D": {
            "x": float(new_euler[0]),
            "y": float(new_euler[1]),
            "z": float(new_euler[2]),
        },
        "points":point_count
    }


def run_icp2(path1, path2, bbox_center, bbox_size, bbox_rotation, ego_transform=None):
    """执行 ICP 配准并返回更新后的 3D 框信息

    Args:
        ego_transform: 4x4变换矩阵，表示自车从frame1到frame2的运动
                      如果为None，则通过点云配准估计全局变换
    """
    pcd1 = o3d.io.read_point_cloud(path1)
    pcd2 = o3d.io.read_point_cloud(path2)

    r = R.from_euler('xyz', bbox_rotation).as_matrix()
    bbox_size_np = np.array(bbox_size)
    # 使用相同大小的裁剪区域以提高配准稳定性
    crop_extent = bbox_size_np * 2.5
    obb = o3d.geometry.OrientedBoundingBox(center=bbox_center, R=r, extent=crop_extent)
    pcd1_crop = pcd1.crop(obb)
    pcd2_crop = pcd2.crop(obb)

    # 检测是否为静止物体
    stationary_result = is_object_stationary(pcd1_crop, pcd2_crop)
    print(f"静止物体检测结果: {stationary_result}")
    print(f"点云1点数: {len(pcd1_crop.points)}, 点云2点数: {len(pcd2_crop.points)}")

    if stationary_result:
        print(f"*** 检测到静止物体，应用自车运动补偿 ***")
        point_count = len(pcd1_crop.points)

        # 如果提供了自车变换矩阵，直接使用
        if ego_transform is not None:
            print(f"使用提供的自车变换矩阵:")
            print(f"自车变换矩阵:\n{ego_transform}")

            ego_transform_inv = np.linalg.inv(ego_transform)
            print(f"自车逆变换矩阵:\n{ego_transform_inv}")

            # 对于静止物体，应用自车运动的逆变换
            bbox_center_homo = np.append(bbox_center, 1.0)
            new_center_homo = np.dot(ego_transform_inv, bbox_center_homo)
            new_center = new_center_homo[:3]

            print(f"原始中心: [{bbox_center[0]:.3f}, {bbox_center[1]:.3f}, {bbox_center[2]:.3f}]")
            print(f"变换后中心: [{new_center[0]:.3f}, {new_center[1]:.3f}, {new_center[2]:.3f}]")

            # 旋转也需要变换
            ego_rotation = ego_transform_inv[:3, :3]
            original_rotation_matrix = R.from_euler('xyz', bbox_rotation).as_matrix()
            new_rotation_matrix = np.dot(ego_rotation, original_rotation_matrix)
            new_euler = R.from_matrix(new_rotation_matrix).as_euler('xyz')

            print(f"原始旋转: [{bbox_rotation[0]:.3f}, {bbox_rotation[1]:.3f}, {bbox_rotation[2]:.3f}]")
            print(f"变换后旋转: [{new_euler[0]:.3f}, {new_euler[1]:.3f}, {new_euler[2]:.3f}]")
        else:
            # 如果没有自车变换信息，估计全局变换
            print(f"未提供自车变换，通过点云配准估计全局变换")
            global_transform = estimate_global_transform(pcd1, pcd2)
            print(f"估计的全局变换矩阵:\n{global_transform}")

            global_transform_inv = np.linalg.inv(global_transform)

            bbox_center_homo = np.append(bbox_center, 1.0)
            new_center_homo = np.dot(global_transform_inv, bbox_center_homo)
            new_center = new_center_homo[:3]

            print(f"原始中心: [{bbox_center[0]:.3f}, {bbox_center[1]:.3f}, {bbox_center[2]:.3f}]")
            print(f"变换后中心: [{new_center[0]:.3f}, {new_center[1]:.3f}, {new_center[2]:.3f}]")

            ego_rotation = global_transform_inv[:3, :3]
            original_rotation_matrix = R.from_euler('xyz', bbox_rotation).as_matrix()
            new_rotation_matrix = np.dot(ego_rotation, original_rotation_matrix)
            new_euler = R.from_matrix(new_rotation_matrix).as_euler('xyz')

            print(f"原始旋转: [{bbox_rotation[0]:.3f}, {bbox_rotation[1]:.3f}, {bbox_rotation[2]:.3f}]")
            print(f"变换后旋转: [{new_euler[0]:.3f}, {new_euler[1]:.3f}, {new_euler[2]:.3f}]")

        return {
            "size3D": {
                "x": float(bbox_size[0]),
                "y": float(bbox_size[1]),
                "z": float(bbox_size[2]),
            },
            "center3D": {
                "x": float(new_center[0]),
                "y": float(new_center[1]),
                "z": float(new_center[2]),
            },
            "rotation3D": {
                "x": float(new_euler[0]),
                "y": float(new_euler[1]),
                "z": float(new_euler[2]),
            },
            "points": point_count,
            "is_stationary": True
        }

    # 参数
    voxel_size = 0.2

    # FPFH 粗配准
    source_down, source_fpfh = preprocess_point_cloud(pcd1_crop, voxel_size)
    target_down, target_fpfh = preprocess_point_cloud(pcd2_crop, voxel_size)

    global_result = execute_global_registration(source_down, target_down, source_fpfh, target_fpfh, voxel_size)

    # ICP 微调
    refine_result = refine_registration(source_down, target_down, global_result.transformation, voxel_size)

    T = refine_result.transformation
    rotation_icp = T[:3, :3]
    translation_icp = T[:3, 3]

    new_center = np.dot(rotation_icp, bbox_center) + translation_icp
    new_rotation_matrix = np.dot(rotation_icp, r)
    new_euler = R.from_matrix(new_rotation_matrix).as_euler('xyz')
    # 统计点数
    point_count = len(pcd1_crop.points)

    return {
        "size3D": {
            "x": float(bbox_size[0]),
            "y": float(bbox_size[1]),
            "z": float(bbox_size[2]),
        },
        "center3D": {
            "x": float(new_center[0]),
            "y": float(new_center[1]),
            "z": float(new_center[2]),
        },
        "rotation3D": {
            "x": float(new_euler[0]),
            "y": float(new_euler[1]),
            "z": float(new_euler[2]),
        },
        "points": point_count
    }
def run_icp2_wrapper(args):
    obj, path1, path2, ego_transform = args
    obj_id = obj.get("objectId")
    size3D = obj.get("size3D", {})
    center3D = obj.get("center3D", {})
    rotation3D = obj.get("rotation3D", {})

    bbox_center = [center3D.get("x"), center3D.get("y"), center3D.get("z")]
    bbox_size = [size3D.get("x"), size3D.get("y"), size3D.get("z")]
    bbox_rotation = [rotation3D.get("x"), rotation3D.get("y"), rotation3D.get("z")]

    # 打印原始框位置
    print(f"=== objectId={obj_id} 处理开始 ===")
    print(f"原始框位置 - center: [{bbox_center[0]:.3f}, {bbox_center[1]:.3f}, {bbox_center[2]:.3f}]")
    print(f"原始框大小 - size: [{bbox_size[0]:.3f}, {bbox_size[1]:.3f}, {bbox_size[2]:.3f}]")
    print(f"原始框旋转 - rotation: [{bbox_rotation[0]:.3f}, {bbox_rotation[1]:.3f}, {bbox_rotation[2]:.3f}]")

    start_time = time.time()
    result = run_icp2(path1, path2, bbox_center, bbox_size, bbox_rotation, ego_transform)
    end_time = time.time()

    print(f"objectId={obj_id} run_icp耗时: {end_time - start_time:.2f} 秒")

    result["objectId"] = obj_id

    # 打印预测框位置
    predicted_center = [result.get("center3D").get("x"), result.get("center3D").get("y"), result.get("center3D").get("z")]
    predicted_rotation = [result.get("rotation3D").get("x"), result.get("rotation3D").get("y"), result.get("rotation3D").get("z")]

    print(f"预测框位置 - center: [{predicted_center[0]:.3f}, {predicted_center[1]:.3f}, {predicted_center[2]:.3f}]")
    print(f"预测框旋转 - rotation: [{predicted_rotation[0]:.3f}, {predicted_rotation[1]:.3f}, {predicted_rotation[2]:.3f}]")

    # 计算位置和旋转变化
    position_change = np.array(predicted_center) - np.array(bbox_center)
    print(f"位置变化 - delta: [{position_change[0]:.3f}, {position_change[1]:.3f}, {position_change[2]:.3f}]")
    print(f"位置变化距离: {np.linalg.norm(position_change):.3f}m")

    # 检查是否为静止物体
    is_stationary = result.get("is_stationary", False)
    if is_stationary:
        print(f"*** 检测为静止物体，已应用自车运动补偿 ***")

    prev_rotation_matrix = R.from_euler("xyz", bbox_rotation).as_matrix()
    rotation_icp = [result.get("rotation3D").get("x"), result.get("rotation3D").get("y"), result.get("rotation3D").get("z")]
    rotation_icp_matrix = R.from_euler("xyz", rotation_icp).as_matrix()

    angle_diff = rotation_matrix_diff_angle(prev_rotation_matrix, rotation_icp_matrix)
    center3D_icp = [result.get("center3D").get("x"), result.get("center3D").get("y"), result.get("center3D").get("z")]
    offset = np.linalg.norm(np.array(bbox_center) - np.array(center3D_icp) )

    # 根据物体大小调整阈值
    volume = bbox_size[0] * bbox_size[1] * bbox_size[2]
    adaptive_rotation_threshold = max(0.02, min(0.05, volume * 0.001))  # 弧度，约1-3度
    adaptive_position_threshold = max(0.1, min(0.5, volume * 0.01))     # 米

    if angle_diff > adaptive_rotation_threshold:
        print(f"*** 旋转变化过大，恢复原始旋转 ***")
        print(f"旋转角度差: {angle_diff:.4f} 弧度 (阈值: {adaptive_rotation_threshold:.4f})")
        result["rotation3D"] = rotation3D
    if offset > adaptive_position_threshold:
        print(f"*** 位置变化过大，恢复原始位置 ***")
        print(f"偏移距离: {offset:.3f}m (阈值: {adaptive_position_threshold:.3f})")
        result["center3D"] = center3D

    # 打印最终结果
    final_center = [result.get("center3D").get("x"), result.get("center3D").get("y"), result.get("center3D").get("z")]
    final_rotation = [result.get("rotation3D").get("x"), result.get("rotation3D").get("y"), result.get("rotation3D").get("z")]
    final_position_change = np.array(final_center) - np.array(bbox_center)

    print(f"最终框位置 - center: [{final_center[0]:.3f}, {final_center[1]:.3f}, {final_center[2]:.3f}]")
    print(f"最终框旋转 - rotation: [{final_rotation[0]:.3f}, {final_rotation[1]:.3f}, {final_rotation[2]:.3f}]")
    print(f"最终位置变化: [{final_position_change[0]:.3f}, {final_position_change[1]:.3f}, {final_position_change[2]:.3f}]")
    print(f"=== objectId={obj_id} 处理完成 ===\n")

    return result
def rotation_matrix_diff_angle(R1, R2):
    """计算两个旋转矩阵之间的夹角（弧度）"""
    R_diff = np.dot(R1.T, R2)
    cos_theta = (np.trace(R_diff) - 1) / 2
    cos_theta = np.clip(cos_theta, -1.0, 1.0)  # 数值稳定
    angle = np.arccos(cos_theta)  # 弧度
    return angle

def is_object_stationary(pcd1_crop, pcd2_crop, threshold=0.1):
    """检测物体是否可能是静止的"""
    points1_count = len(pcd1_crop.points)
    points2_count = len(pcd2_crop.points)

    print(f"静止检测 - 点云1点数: {points1_count}, 点云2点数: {points2_count}")

    if points1_count < 10 or points2_count < 10:
        print(f"点数太少，假设为静止物体")
        return True  # 点太少，假设静止

    # 计算点云重心
    center1 = np.mean(np.asarray(pcd1_crop.points), axis=0)
    center2 = np.mean(np.asarray(pcd2_crop.points), axis=0)

    print(f"点云1重心: [{center1[0]:.3f}, {center1[1]:.3f}, {center1[2]:.3f}]")
    print(f"点云2重心: [{center2[0]:.3f}, {center2[1]:.3f}, {center2[2]:.3f}]")

    # 如果重心距离很小，可能是静止的
    distance = np.linalg.norm(center1 - center2)
    print(f"重心距离: {distance:.3f}m, 阈值: {threshold:.3f}m")

    is_stationary = distance < threshold
    print(f"静止判断结果: {is_stationary}")

    return is_stationary

def estimate_global_transform(pcd1, pcd2, voxel_size=0.5):
    """估计两个点云之间的全局变换（自车运动）"""
    try:
        # 下采样以提高速度
        pcd1_down = pcd1.voxel_down_sample(voxel_size)
        pcd2_down = pcd2.voxel_down_sample(voxel_size)

        # 估计法线
        pcd1_down.estimate_normals(o3d.geometry.KDTreeSearchParamHybrid(radius=voxel_size * 2, max_nn=30))
        pcd2_down.estimate_normals(o3d.geometry.KDTreeSearchParamHybrid(radius=voxel_size * 2, max_nn=30))

        # 计算FPFH特征
        fpfh1 = o3d.pipelines.registration.compute_fpfh_feature(
            pcd1_down, o3d.geometry.KDTreeSearchParamHybrid(radius=voxel_size * 5, max_nn=100))
        fpfh2 = o3d.pipelines.registration.compute_fpfh_feature(
            pcd2_down, o3d.geometry.KDTreeSearchParamHybrid(radius=voxel_size * 5, max_nn=100))

        # 全局配准
        result = execute_global_registration(pcd1_down, pcd2_down, fpfh1, fpfh2, voxel_size)

        # ICP精配准
        refined_result = refine_registration(pcd1_down, pcd2_down, result.transformation, voxel_size)

        return refined_result.transformation
    except Exception as e:
        print(f"全局变换估计失败: {e}")
        return np.eye(4)  # 返回单位矩阵作为fallback

def parse_ego_pose(ego_pose_data):
    """解析自车位姿数据并构建4x4变换矩阵

    Args:
        ego_pose_data: 包含自车位姿信息的字典，可能的格式：
                      - {"translation": [x, y, z], "rotation": [rx, ry, rz]}
                      - {"transform_matrix": [[...], [...], [...], [...]]}
                      - {"pose1": {...}, "pose2": {...}}

    Returns:
        4x4 numpy array representing the transformation matrix
    """
    try:
        if "transform_matrix" in ego_pose_data:
            # 直接提供变换矩阵
            return np.array(ego_pose_data["transform_matrix"])

        elif "translation" in ego_pose_data and "rotation" in ego_pose_data:
            # 提供平移和旋转
            translation = np.array(ego_pose_data["translation"])
            rotation = np.array(ego_pose_data["rotation"])

            # 构建变换矩阵
            rotation_matrix = R.from_euler('xyz', rotation).as_matrix()
            transform = np.eye(4)
            transform[:3, :3] = rotation_matrix
            transform[:3, 3] = translation
            return transform

        elif "pose1" in ego_pose_data and "pose2" in ego_pose_data:
            # 提供两个位姿，计算相对变换
            pose1 = ego_pose_data["pose1"]
            pose2 = ego_pose_data["pose2"]

            # 构建两个位姿的变换矩阵
            T1 = build_transform_matrix(pose1)
            T2 = build_transform_matrix(pose2)

            # 计算相对变换 T1 -> T2
            return np.dot(T2, np.linalg.inv(T1))

        else:
            print("未识别的自车位姿数据格式")
            return None

    except Exception as e:
        print(f"解析自车位姿失败: {e}")
        return None

def build_transform_matrix(pose):
    """从位姿字典构建4x4变换矩阵"""
    translation = np.array([pose.get("x", 0), pose.get("y", 0), pose.get("z", 0)])
    rotation = np.array([pose.get("rx", 0), pose.get("ry", 0), pose.get("rz", 0)])

    rotation_matrix = R.from_euler('xyz', rotation).as_matrix()
    transform = np.eye(4)
    transform[:3, :3] = rotation_matrix
    transform[:3, 3] = translation
    return transform
@app.route('/predict', methods=['POST'])
def predict():
    data = request.get_json()
    # print("predict param 0617:", data)

    # 获取参数
    path1 = data.get('pcd1Url', DEFAULT_PCD1_PATH)
    path2 = data.get('pcd2Url', DEFAULT_PCD2_PATH)
    objects = data.get('objects', [])  # 可选数组参数

    # 获取自车位姿信息（可选）
    ego_pose_data = data.get('egoPose', None)
    ego_transform = None
    if ego_pose_data:
        # 解析自车位姿信息并构建变换矩阵
        ego_transform = parse_ego_pose(ego_pose_data)

    print("=== 预测请求开始 ===")
    print(f"点云文件1: {path1}")
    print(f"点云文件2: {path2}")
    print(f"物体数量: {len(objects)}")
    print(f"自车位姿信息: {'已提供' if ego_transform is not None else '未提供'}")

    if ego_transform is not None:
        print(f"自车变换矩阵:\n{ego_transform}")

    print("处理的物体列表:")
    for i, obj in enumerate(objects):
        obj_id = obj.get("objectId", f"obj_{i}")
        center = obj.get("center3D", {})
        print(f"  {i+1}. objectId: {obj_id}, center: [{center.get('x', 0):.3f}, {center.get('y', 0):.3f}, {center.get('z', 0):.3f}]")
    # 创建临时目录
    with tempfile.TemporaryDirectory() as temp_dir:
        try:
            # 记录下载开始时间
            download_start_time = time.time()
            # 下载远程文件到本地临时路径
            path1 = download_file_from_url(path1, temp_dir)

            download_end_time = time.time()

            path2 = download_file_from_url(path2, temp_dir)
            # 记录下载结束时间并计算耗时

            download_duration = download_end_time - download_start_time
            print("下载文件耗时: {:.2f} 秒".format(download_duration))

            print("temp file path :", path1,path2)

            # results = []
            # for obj in objects:
            #     obj_id = obj.get("objectId")
            #     size3D = obj.get("size3D", {})
            #     center3D = obj.get("center3D", {})
            #     rotation3D = obj.get("rotation3D", {})
            #
            #     bbox_center = [center3D.get("x"), center3D.get("y"), center3D.get("z")]
            #     bbox_size = [size3D.get("x"), size3D.get("y"), size3D.get("z")]
            #     bbox_rotation = [rotation3D.get("x"), rotation3D.get("y"), rotation3D.get("z")]
            #     time1 = time.time()
            #     result = run_icp2(path1, path2, bbox_center, bbox_size, bbox_rotation)
            #     time2 = time.time()
            #     print("run_icp耗时: {:.2f} 秒".format(time2 - time1))
            #     result["objectId"] = obj_id
            #     results.append(result)
            arg_list = [(obj, path1, path2, ego_transform) for obj in objects]
            with ThreadPoolExecutor(max_workers=min(8, len(objects))) as executor:
                results = list(executor.map(run_icp2_wrapper, arg_list))
                print("results:", results)
        except Exception as e:
            print(f"An error occurred: {str(e)}")
            traceback.print_exc()
            return jsonify({
                "status": "fail",
                "result": str(e),
                "pcd1": path1,
                "pcd2": path2
            })
        finally:
            # 清理临时文件（TemporaryDirectory 会自动清理）
            pass

    return jsonify({
        "status": "success",
        "result": results,
        "pcd1": path1,
        "pcd2": path2
    })


def download_file_from_url(url, temp_dir):
    """从URL下载文件到临时目录（分块下载、无重试）"""
    headers = {
        'User-Agent': 'Mozilla/5.0 (compatible; FileDownloader/1.0)'
    }

    try:
        with requests.get(url, stream=True, timeout=10, headers=headers) as response:
            response.raise_for_status()  # 自动抛出非200状态异常

            suffix = ".pcd"
            temp_file = tempfile.NamedTemporaryFile(dir=temp_dir, suffix=suffix, delete=False)

            with open(temp_file.name, 'wb') as f:
                shutil.copyfileobj(response.raw, f)

            print("url:", url)
            print("temp file path:", temp_file.name)
            return temp_file.name

    except Exception as e:
        raise RuntimeError(f"下载失败: {e}")


if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5000, debug=True)
