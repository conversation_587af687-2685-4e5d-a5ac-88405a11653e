# Debug指南 - 详细日志系统

## 概述

我已经为您的算法添加了完整的日志系统，包括时间戳、输入输出数据、处理过程等详细信息，方便进行debug。

## 日志功能

### 1. 日志输出位置
- **Console输出**: 实时查看处理过程
- **文件输出**: `object_tracking.log` - 持久化存储，便于后续分析

### 2. 日志格式
```
[时间戳][请求ID][物体ID] 消息内容
```

示例：
```
[2024-01-15 14:30:15.123][REQ:a1b2c3d4][OBJ:test_obj] 开始处理物体跟踪
```

### 3. 日志级别
- **INFO**: 正常处理信息
- **WARNING**: 警告信息（如阈值修正）
- **ERROR**: 错误信息

## 详细日志内容

### 请求级别
```
=== 新的预测请求开始 ===
接收到请求数据: {...}
点云文件1: /path/to/frame1.pcd
点云文件2: /path/to/frame2.pcd
物体数量: 2
自车位姿信息: 已提供/未提供
解析的自车变换矩阵: [4x4矩阵]
```

### 文件下载
```
开始下载文件: https://example.com/frame1.pcd
文件大小: 15.23 MB
文件下载完成: /tmp/tmpxxx.pcd
下载耗时: 2.456秒, 文件大小: 15.23MB
下载速度: 6.21MB/s
```

### 物体处理
```
=== 开始处理物体 ===
输入中心位置: [  5.0000   2.0000   0.0000]
输入框大小: [  2.0000   1.0000   1.5000]
输入旋转角度: [  0.0000   0.0000   0.0000]
点云加载成功 - PCD1: 15234点, PCD2: 15187点
点云裁剪完成 - PCD1裁剪后: 156点, PCD2裁剪后: 148点
```

### 静止物体检测
```
开始静止物体检测
静止检测 - 点云1点数: 156, 点云2点数: 148
点云1重心: [  5.1230   2.0450   0.0120]
点云2重心: [  5.1340   2.0510   0.0150]
重心距离: 0.0250m, 阈值: 0.1000m
静止判断结果: True
```

### 自车运动补偿
```
*** 检测到静止物体，应用自车运动补偿 ***
自车逆变换矩阵:
  [0] [  0.9962   0.0872   0.0000  -0.9962]
  [1] [ -0.0872   0.9962   0.0000  -0.0872]
  [2] [  0.0000   0.0000   1.0000   0.0000]
  [3] [  0.0000   0.0000   0.0000   1.0000]
原始中心: [  5.0000   2.0000   0.0000]
变换后中心: [  4.1591   1.6438   0.0000]
位置变化: [ -0.8409  -0.3562   0.0000]
位置变化距离: 0.9140m
```

### ICP配准（运动物体）
```
*** 检测到运动物体，执行ICP配准 ***
ICP参数 - 体素大小: 0.2
开始FPFH特征计算
下采样后点数 - 源: 78, 目标: 74
开始全局配准
全局配准适应度: 0.8234, RMSE: 0.1456
开始ICP精配准
ICP精配准适应度: 0.9123, RMSE: 0.0987
ICP总变换矩阵: [4x4矩阵]
分离自车运动和物体运动
分离出的物体运动矩阵: [4x4矩阵]
```

### 阈值检查
```
自适应阈值 - 旋转: 0.0300弧度, 位置: 0.200m
旋转角度差: 0.0150弧度
位置偏移: 0.1500m
未触发阈值修正
```

### 最终结果
```
最终框位置: [  4.1591   1.6438   0.0000]
最终框旋转: [  0.0000   0.0000  -0.0870]
最终位置变化: [ -0.8409  -0.3562   0.0000]
最终位置变化距离: 0.9140m
物体处理完成，耗时: 0.234秒
=== 物体处理完成 ===
```

## 使用工具

### 1. 测试日志系统
```bash
python test_logging_system.py
```

### 2. 分析日志文件
```bash
python log_analyzer.py
```

这会生成详细的分析报告，包括：
- 请求统计
- 物体处理统计
- 性能分析
- 错误统计

### 3. 实时监控日志
```bash
tail -f object_tracking.log
```

## Debug技巧

### 1. 关键信息查找
- 搜索 `***` 找到重要处理节点
- 搜索 `ERROR` 找到错误信息
- 搜索 `WARNING` 找到警告信息
- 搜索物体ID找到特定物体的处理过程

### 2. 性能分析
- 查看各阶段耗时
- 分析下载速度
- 监控点云处理时间
- 对比不同物体的处理时间

### 3. 算法验证
- 检查输入输出位置变化
- 验证自车变换矩阵
- 确认静止/运动物体检测准确性
- 观察阈值触发情况

### 4. 错误排查
- 查看完整的错误堆栈
- 分析失败的处理步骤
- 检查输入数据有效性
- 验证文件下载状态

## API响应增强

现在API响应包含更多调试信息：

```json
{
  "status": "success",
  "result": [...],
  "request_id": "a1b2c3d4-...",
  "processing_time": {
    "download": 2.456,
    "processing": 1.234,
    "total": 3.690
  },
  "statistics": {
    "total_objects": 2,
    "stationary_objects": 1,
    "moving_objects": 1,
    "error_objects": 0
  },
  "timestamp": "2024-01-15T14:30:15.123456"
}
```

## 常见问题排查

### 1. 位置没有变化
- 检查自车变换矩阵是否正确
- 确认静止物体检测是否准确
- 验证逆变换计算过程

### 2. ICP配准失败
- 查看点云点数是否足够
- 检查FPFH特征计算
- 分析配准适应度和RMSE

### 3. 阈值频繁触发
- 调整自适应阈值参数
- 分析物体大小和变化幅度
- 检查ICP配准质量

现在您可以通过详细的日志系统全面了解算法的每一个处理步骤，快速定位问题所在！
