# Console日志输出说明

## 概述

为了帮助排查预测算法的问题，我已经在代码中添加了详细的console日志输出。这些日志会显示：

1. **原始框位置和预测框位置**
2. **静止物体检测过程**
3. **自车运动补偿计算**
4. **阈值检查和位置修正**

## 日志结构

### 1. 请求开始日志
```
=== 预测请求开始 ===
点云文件1: /path/to/frame1.pcd
点云文件2: /path/to/frame2.pcd
物体数量: 2
自车位姿信息: 已提供/未提供
```

### 2. 每个物体的处理日志

#### 2.1 处理开始
```
=== objectId=obj_001 处理开始 ===
原始框位置 - center: [5.000, 2.000, 0.000]
原始框大小 - size: [2.000, 1.000, 1.500]
原始框旋转 - rotation: [0.000, 0.000, 0.000]
```

#### 2.2 静止物体检测
```
静止物体检测结果: True/False
点云1点数: 150, 点云2点数: 145
静止检测 - 点云1点数: 150, 点云2点数: 145
点云1重心: [5.123, 2.045, 0.012]
点云2重心: [5.134, 2.051, 0.015]
重心距离: 0.025m, 阈值: 0.100m
静止判断结果: True
```

#### 2.3 静止物体的自车运动补偿
```
*** 检测到静止物体，应用自车运动补偿 ***
使用提供的自车变换矩阵:
自车变换矩阵:
[[ 0.9961947  -0.08715574  0.          1.        ]
 [ 0.08715574  0.9961947   0.          0.        ]
 [ 0.          0.          1.          0.        ]
 [ 0.          0.          0.          1.        ]]
原始中心: [5.000, 2.000, 0.000]
变换后中心: [4.159, 1.644, 0.000]
原始旋转: [0.000, 0.000, 0.000]
变换后旋转: [0.000, 0.000, -0.087]
```

#### 2.4 预测结果
```
objectId=obj_001 run_icp耗时: 0.05 秒
预测框位置 - center: [4.159, 1.644, 0.000]
预测框旋转 - rotation: [0.000, 0.000, -0.087]
位置变化 - delta: [-0.841, -0.356, 0.000]
位置变化距离: 0.914m
*** 检测为静止物体，已应用自车运动补偿 ***
```

#### 2.5 阈值检查和修正（如果需要）
```
*** 旋转变化过大，恢复原始旋转 ***
旋转角度差: 0.1500 弧度 (阈值: 0.0500)

*** 位置变化过大，恢复原始位置 ***
偏移距离: 2.500m (阈值: 0.300)
```

#### 2.6 最终结果
```
最终框位置 - center: [4.159, 1.644, 0.000]
最终框旋转 - rotation: [0.000, 0.000, -0.087]
最终位置变化: [-0.841, -0.356, 0.000]
=== objectId=obj_001 处理完成 ===
```

## 关键指标解读

### 位置变化分析
- **位置变化距离**: 原始位置到预测位置的欧几里得距离
- **位置变化向量**: 显示在X、Y、Z轴上的具体变化
- **对于静止物体**: 位置变化应该反映自车的运动

### 静止物体判断
- **重心距离**: 两帧点云重心的距离
- **点数检查**: 如果点数太少(<10)，假设为静止
- **阈值**: 默认0.1米，可以调整

### 自车运动补偿
- **有自车位姿**: 直接使用提供的变换矩阵
- **无自车位姿**: 通过全局点云配准估计
- **逆变换**: 对静止物体应用自车运动的逆变换

## 排查建议

1. **检查静止物体检测**: 看重心距离是否合理
2. **验证自车变换**: 检查变换矩阵是否符合预期
3. **观察位置变化**: 静止物体的变化应该与自车运动相反
4. **监控阈值触发**: 看是否有过多的位置/旋转修正

## 调试技巧

1. **关注"***"标记的重要信息**
2. **比较原始位置和最终位置的变化**
3. **检查自车变换矩阵的合理性**
4. **观察静止物体检测的准确性**
